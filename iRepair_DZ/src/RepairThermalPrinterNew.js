import QRCodeUtils from './QRCodeUtils.js';
import { getTranslation } from './translations.js';

/**
 * Enhanced Thermal Printer for Repair Management System
 * Supports both 40x60mm QR tickets and 80x80mm full receipts
 */
class RepairThermalPrinter {
  constructor() {
    this.qrTicketWidth = '40mm';
    this.qrTicketHeight = '60mm';
    this.thermalWidth = '80mm';
    this.thermalHeight = '80mm';
    this.isInitialized = false;
    this.init();
  }

  async init() {
    try {
      this.isInitialized = true;
      console.log('🖨️ Repair Thermal Printer: System initialized successfully');
    } catch (error) {
      console.warn('🖨️ Repair Thermal Printer: Initialization failed', error);
      this.isInitialized = true;
    }
  }

  formatPrice(price, language) {
    const numPrice = parseFloat(price) || 0;
    const formattedPrice = Math.round(numPrice).toString();

    // Return price with appropriate currency based on language
    if (language === 'ar') {
      return `${formattedPrice} د.ج`;
    } else {
      return `${formattedPrice} DZD`;
    }
  }

  // Format price without currency for table display
  formatPriceClean(price) {
    const numPrice = parseFloat(price) || 0;
    return Math.round(numPrice).toString();
  }

  // Format phone number to display on two lines for cleaner appearance
  formatPhoneNumber(phoneNumber) {
    if (!phoneNumber) return '';

    const phone = phoneNumber.toString();
    // Split phone number roughly in half
    const midPoint = Math.ceil(phone.length / 2);
    const firstPart = phone.substring(0, midPoint);
    const secondPart = phone.substring(midPoint);

    return `${firstPart}<br/>${secondPart}`;
  }

  async generatePasteTicketContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;

      // Generate barcode for the repair
      const barcodeImage = QRCodeUtils.generateRepairBarcode(repair.repairBarcode || repair.id);

      // Calculate interest rate = total price - parts price
      const totalPrice = repair.repairPrice || 0;
      const partsPrice = repair.partsPrice || 0;
      const interestRate = totalPrice - partsPrice;
      const displayPrice = interestRate > 0 ? interestRate : totalPrice;

      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Paste Ticket - ${repair.clientName}</title>
          <style>
            @page {
              size: 45mm 58mm portrait;
              margin: 1mm;
            }

            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');

            body {
              font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', 'Arial', sans-serif"};
              font-size: ${language === 'ar' ? '14px' : '13px'};
              font-weight: ${language === 'ar' ? '700' : '900'};
              line-height: 1.1;
              margin: 0;
              padding: ${language === 'ar' ? '1.5mm' : '1mm'};
              width: 43mm;
              height: 56mm;
              direction: ${isRTL ? 'rtl' : 'ltr'};
              text-align: center;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-start;
            }

            /* Logo and Price in one line at top middle */
            .header-line {
              display: flex;
              justify-content: center;
              align-items: center;
              margin-bottom: 2mm;
              width: 100%;
              gap: 2mm;
            }

            .header-line .logo {
              ${storeSettings.storeLogo ? '' : 'font-size: 12px; font-weight: bold;'}
            }

            .header-line .logo img {
              width: 12mm;
              height: 6mm;
              object-fit: contain;
              display: block;
            }

            .header-line .price {
              font-weight: ${language === 'ar' ? '900' : '900'};
              font-size: ${language === 'ar' ? '13px' : '12px'};
              border: 2px solid black;
              padding: 0.5mm 1.5mm;
              background: #f0f0f0;
              font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
              direction: ltr;
              border-radius: 1mm;
            }

            /* Client Name - Bold and Big */
            .client-name {
              font-weight: ${language === 'ar' ? '900' : '900'};
              font-size: ${language === 'ar' ? '16px' : '15px'};
              margin-bottom: 0.2mm;
              text-align: center;
              text-transform: uppercase;
              font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', 'Arial', sans-serif"};
              color: #000;
              line-height: 1.0;
            }

            /* Phone Number - More space, single line display */
            .client-phone-number {
              font-weight: ${language === 'ar' ? '700' : '900'};
              font-size: ${language === 'ar' ? '12px' : '11px'};
              margin-bottom: 0.3mm;
              text-align: center;
              font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', 'Arial', sans-serif"};
              direction: ltr;
              color: #333;
              padding: 0 1mm;
            }

            /* Device Name only */
            .device-name {
              font-weight: ${language === 'ar' ? '700' : '900'};
              font-size: ${language === 'ar' ? '12px' : '11px'};
              margin-bottom: 0.5mm;
              text-align: center;
              font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', 'Arial', sans-serif"};
              color: #333;
            }

            /* Barcode at bottom */
            .barcode-section {
              text-align: center;
              margin-top: 1mm;
              width: 100%;
            }

            .barcode-section img {
              width: 38mm;
              height: 7mm;
              display: block;
              margin: 0 auto;
            }
          </style>
        </head>
        <body>
          <!-- Logo and Price in one line at top middle -->
          <div class="header-line">
            <div class="logo">
              ${storeSettings.storeLogo ?
                `<img src="${storeSettings.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />` :
                `🏪`
              }
            </div>
            <div class="price">${this.formatPrice(displayPrice, language)}</div>
          </div>

          <!-- Client Name (Bold and Big) -->
          <div class="client-name">${repair.clientName}</div>

          <!-- Phone Number (Big and Bold, no label) - Single line -->
          ${repair.clientPhone ? `<div class="client-phone-number">${repair.clientPhone}</div>` : ''}

          <!-- Device Name only (no problem description) -->
          <div class="device-name">${repair.deviceName}</div>

          <!-- Barcode at bottom -->
          <div class="barcode-section">
            <img src="${barcodeImage}" alt="Barcode" />
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating paste ticket content:', error);
      return null;
    }
  }

  async generateRepairTicketContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;

      // Generate barcode for the repair
      const barcodeImage = QRCodeUtils.generateRepairBarcode(repair.repairBarcode || repair.id);

      // Calculate interest rate = total price - parts price (don't add parts to total)
      const totalPrice = repair.repairPrice || 0;
      const partsPrice = repair.partsPrice || 0;
      const interestRate = totalPrice - partsPrice;

      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      const content = `
        <!DOCTYPE html>
        <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${t('repairTicket', 'تذكرة الإصلاح')}</title>
          <style>
            ${this.getThermalTicketStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-ticket">
            <!-- Header Section -->
            <div class="ticket-header">
              <div class="store-logo">
                ${storeSettings.storeLogo ?
                  `<img src="${storeSettings.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />` :
                  `🏪`
                }
              </div>
              <div class="store-name">${storeSettings.storeName || 'ICALDZ STORE'}</div>
              <div class="store-phone">📞 ${storeSettings.storePhone || '+213 555 123 456'}</div>
              <div class="store-address">${storeSettings.storeAddress || 'الجزائر العاصمة، الجزائر'}</div>
            </div>

            <div class="separator"></div>

            <!-- Ticket Title -->
            <div class="ticket-title">${t('repairTicket', 'تذكرة الإصلاح')}</div>
            <div class="ticket-subtitle">${t('repairSystem', 'نظام الإصلاح - ICALDZ')}</div>

            <div class="separator"></div>

            <!-- Client Information -->
            <div class="info-section">
              <div class="info-line">
                <span class="label">${t('ticketNumber', 'رقم التذكرة')}:</span>
                <span class="value">${repair.id || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('date', 'التاريخ')}:</span>
                <span class="value">${new Date(repair.date || Date.now()).toLocaleDateString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('clientName', 'اسم العميل')}:</span>
                <span class="value">${repair.clientName || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('clientPhone', 'هاتف العميل')}:</span>
                <span class="value">${repair.clientPhone || repair.phone || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('deviceName', 'اسم الجهاز')}:</span>
                <span class="value">${repair.deviceName || 'N/A'}</span>
              </div>
              ${repair.problemDescription || repair.problemType ? `
              <div class="info-line">
                <span class="label">${t('problem', 'المشكلة')}:</span>
                <span class="value">${repair.problemDescription || repair.problemType || 'N/A'}</span>
              </div>
              ` : ''}
              ${repair.remarks ? `
              <div class="info-line">
                <span class="label">${t('remarks', 'ملاحظات')}:</span>
                <span class="value">${repair.remarks}</span>
              </div>
              ` : ''}
            </div>

            <div class="separator-dotted"></div>

            <!-- Barcode Section -->
            ${barcodeImage ? `
            <div class="barcode-section">
              <img src="${barcodeImage}" alt="Barcode" class="barcode-image" />
              <div class="barcode-text">${repair.repairBarcode || repair.id}</div>
            </div>
            ` : `
            <div class="barcode-section">
              <div class="barcode-text-only">${repair.repairBarcode || repair.id}</div>
            </div>
            `}

            <div class="separator-dotted"></div>

            <!-- Price Section -->
            <div class="price-section">
              <div class="price-line">
                <span class="label">${t('repairPrice', 'سعر الإصلاح')}:</span>
                <span class="value">${this.formatPrice(repair.repairPrice || 0, language)}</span>
              </div>
              ${(repair.partsPrice && parseFloat(repair.partsPrice) > 0) ? `
              <div class="price-line">
                <span class="label">${t('partsPrice', 'سعر القطع')}:</span>
                <span class="value">${this.formatPrice(repair.partsPrice, language)}</span>
              </div>
              <div class="price-line">
                <span class="label">${t('interestRate', 'معدل الفائدة')}:</span>
                <span class="value">${this.formatPrice(interestRate, language)}</span>
              </div>
              ` : ''}
              <div class="total-price">
                <span class="label">${t('totalAmount', 'المبلغ الإجمالي')}:</span>
                <span class="value">${this.formatPrice(totalPrice, language)}</span>
              </div>
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="ticket-footer">
              <div class="thank-you">${t('thankYou', 'شكراً لثقتكم')}</div>
              <div class="developer-info">
                <div>Développé par iCode DZ</div>
                <div>📞 0551930589</div>
              </div>
              <div class="print-time">${t('printedOn', 'طُبع في')}: ${new Date().toLocaleString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating repair ticket content:', error);
      return null;
    }
  }

  getThermalTicketStyles(language, isRTL) {
    return `
      * { margin: 0; padding: 0; box-sizing: border-box; }
      @page { size: 80mm auto; margin: 0; }
      @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');

      body {
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        font-size: ${language === 'ar' ? '14px' : '13px'};
        line-height: 1.3;
        color: #000;
        background: white;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        direction: ${language === 'ar' ? 'rtl' : 'ltr'};
        width: 80mm;
        margin: 0 auto;
        padding: ${language === 'ar' ? '2mm 6mm' : '2mm 4mm'};
        text-align: center;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        ${language === 'ar' ? 'transform: translateX(2mm);' : ''}
      }

      .thermal-ticket {
        width: 100%;
        text-align: center;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      .ticket-header {
        text-align: center;
        margin-bottom: 3mm;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-logo {
        font-size: 20px;
        margin-bottom: 2mm;
        text-align: center;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-logo img {
        width: 25mm;
        height: 10mm;
        object-fit: contain;
        display: block;
        margin: 0 auto 1mm auto;
        ${language === 'ar' ? 'margin-left: calc(50% + 2mm);' : ''}
      }
      .store-name {
        font-size: ${language === 'ar' ? '16px' : '15px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        margin-bottom: 1mm;
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-phone {
        font-size: ${language === 'ar' ? '13px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        margin-bottom: 1mm;
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
        direction: ltr;
      }
      .store-address {
        font-size: ${language === 'ar' ? '12px' : '11px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        margin-bottom: 2mm;
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      .ticket-title {
        font-size: ${language === 'ar' ? '15px' : '14px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        text-align: center;
        margin: 2mm 0;
        border: 2px solid #000;
        padding: 2mm;
        background: #f0f0f0;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .ticket-subtitle {
        font-size: ${language === 'ar' ? '12px' : '11px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        text-align: center;
        margin-bottom: 2mm;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      .separator {
        border-bottom: 2px solid #000;
        margin: 2mm 0;
        width: 100%;
      }
      .separator-dotted {
        border-bottom: 1px dashed #000;
        margin: 2mm 0;
        width: 100%;
      }

      .info-section {
        margin: 2mm 0;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .info-line {
        display: flex;
        justify-content: space-between;
        margin: 1mm 0;
        font-size: ${language === 'ar' ? '13px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .info-line .label {
        font-weight: ${language === 'ar' ? '700' : '900'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
      }
      .info-line .value {
        text-align: ${isRTL ? 'left' : 'right'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
      }

      .barcode-section {
        text-align: center;
        margin: 3mm 0;
        padding: 2mm;
        border: 2px solid #000;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .barcode-image {
        max-width: 100%;
        height: auto;
        margin: 0 auto 3mm auto;
        display: block;
      }
      .barcode-text {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        font-weight: 900;
        text-align: center;
      }
      .barcode-text-only {
        font-family: 'Courier New', monospace;
        font-size: 14px;
        font-weight: 900;
        padding: 4mm;
        text-align: center;
      }

      .price-section {
        margin: 2mm 0;
      }
      .price-line {
        display: flex;
        justify-content: space-between;
        margin: 1mm 0;
        font-size: ${language === 'ar' ? '13px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
      }
      .price-line .label {
        font-weight: ${language === 'ar' ? '700' : '900'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
      }
      .price-line .value {
        font-weight: ${language === 'ar' ? '700' : '900'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
      }
      .total-price {
        display: flex;
        justify-content: space-between;
        margin: 2mm 0;
        font-size: ${language === 'ar' ? '15px' : '14px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        border: 2px solid #000;
        padding: 2mm;
        background: #f0f0f0;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
      }

      /* Footer Styles - Bigger and Clearer */
      .ticket-footer {
        text-align: center;
        margin-top: 4mm;
        font-size: 12px;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
        width: 100%;
      }
      .thank-you {
        font-weight: ${language === 'ar' ? '700' : '900'};
        margin-bottom: 3mm;
        font-size: 14px;
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
      }
      .developer-info {
        border-top: 2px solid #000;
        padding-top: 3mm;
        margin: 3mm 0;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
      }
      .print-time {
        font-size: 10px;
        margin-top: 3mm;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma'" : "'Arial Black', 'Arial'"}, sans-serif;
      }

      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          text-align: center;
        }
        .thermal-ticket {
          page-break-inside: avoid;
          text-align: center;
          margin: 0 auto;
        }
        * {
          text-align: center !important;
        }
      }
    `;
  }

  async openPrintWindow(repair, options = {}) {
    try {
      const content = await this.generateRepairTicketContent(repair, options);
      if (!content) {
        throw new Error('Failed to generate ticket content');
      }

      const printWindow = window.open('', '_blank', 'width=400,height=700');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error opening print window:', error);
      return false;
    }
  }

  async openPasteTicketPrintWindow(repair, options = {}) {
    try {
      const content = await this.generatePasteTicketContent(repair, options);
      if (!content) {
        throw new Error('Failed to generate paste ticket content');
      }

      const printWindow = window.open('', '_blank', 'width=300,height=200');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error opening paste ticket print window:', error);
      return false;
    }
  }

  async printRepairOrderReceipt(repair, options = {}) {
    // Use the repair ticket method for repair orders
    return this.openPrintWindow(repair, options);
  }

  async printClientPickupReceipt(repair, options = {}) {
    try {
      const content = await this.generateClientInvoiceContent(repair, options);
      if (!content) {
        throw new Error('Failed to generate client invoice content');
      }

      const printWindow = window.open('', '_blank', 'width=400,height=700');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error printing client pickup receipt:', error);
      return false;
    }
  }

  async generateClientInvoiceContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;
      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      // Calculate pricing - interest rate = total price - parts price
      const repairPrice = parseFloat(repair.repairPrice || 0);
      const partsPrice = parseFloat(repair.partsPrice || 0);
      const interestRate = repairPrice - partsPrice;
      const subtotal = repairPrice; // Don't add parts price to total

      // ✅ FIXED: Tax Synchronization from Store Settings
      // Tax rate is now synchronized from store settings with 0 as default
      const taxRate = parseFloat(storeSettings.taxRate || 0);
      const taxAmount = (subtotal * taxRate) / 100;
      const finalTotal = subtotal + taxAmount;

      // Generate invoice number
      const invoiceNumber = `INV-${Date.now()}`;

      const content = `
        <!DOCTYPE html>
        <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${t('clientInvoice', 'فاتورة العميل')}</title>
          <style>
            ${this.getThermalInvoiceStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-invoice">
            <!-- Header Section -->
            <div class="invoice-header">
              <div class="store-logo">
                ${storeSettings.storeLogo ?
                  `<img src="${storeSettings.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />` :
                  `🏪`
                }
              </div>
              <div class="store-name">${storeSettings.storeName || 'ICALDZ STORE'} ⭐</div>
              <div class="store-phone">📞 ${storeSettings.storePhone || '+213 555 123 456'}</div>
              <div class="store-address">${storeSettings.storeAddress || 'الجزائر العاصمة، الجزائر'}</div>
            </div>

            <div class="separator"></div>

            <!-- Invoice Title -->
            <div class="invoice-title">${t('clientInvoice', 'فاتورة العميل')}</div>
            <div class="invoice-subtitle">${t('repairSystem', 'نظام الإصلاح - ICALDZ')}</div>

            <div class="separator"></div>

            <!-- Invoice Details -->
            <div class="invoice-details">
              <div class="detail-line">
                <span class="label">${t('invoiceNumber', 'رقم الفاتورة')}:</span>
                <span class="value">${invoiceNumber}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('date', 'التاريخ')}:</span>
                <span class="value">${new Date().toLocaleDateString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('time', 'الوقت')}:</span>
                <span class="value">${new Date().toLocaleTimeString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('clientName', 'اسم العميل')}:</span>
                <span class="value">${repair.clientName || 'Client de passage'}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('paymentMode', 'طريقة الدفع')}:</span>
                <span class="value">${t('cash', 'نقداً')}</span>
              </div>
            </div>

            <div class="separator-dotted"></div>

            <!-- Products Table -->
            <div class="products-table">
              <div class="table-header">
                <span class="col-product">${t('service', 'الخدمة')}</span>
                <span class="col-total">${language === 'ar' ? t('total', 'المجموع') : 'Prix Total'}</span>
              </div>
              <div class="table-row">
                <span class="col-product">${repair.deviceName || ''}</span>
                <span class="col-total">${this.formatPriceClean(repairPrice)}</span>
              </div>
            </div>

            <div class="separator-dotted"></div>

            <!-- Totals Section -->
            <div class="totals-section">
              <div class="total-line">
                <span class="label">${t('subtotal', 'المجموع الفرعي')}:</span>
                <span class="value">${this.formatPrice(subtotal, language)}</span>
              </div>
              <div class="total-line">
                <span class="label">${t('tax', 'الضريبة')} ${taxRate}%:</span>
                <span class="value">${this.formatPrice(taxAmount, language)}</span>
              </div>
              <div class="final-total">
                <span class="label">${t('finalTotal', 'المجموع النهائي')}:</span>
                <span class="value">${this.formatPrice(finalTotal, language)}</span>
              </div>
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="invoice-footer">
              <div class="thank-you">${t('thankYouVisit', 'شكراً لزيارتكم')}</div>
              <div class="developer-info">
                <div>Développé par iCode DZ</div>
                <div>📞 0551930589</div>
              </div>
              <div class="print-time">${t('printedOn', 'طُبع في')}: ${new Date().toLocaleString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating client invoice content:', error);
      return null;
    }
  }

  getThermalInvoiceStyles(language, isRTL) {
    return `
      * { margin: 0; padding: 0; box-sizing: border-box; }
      @page { size: 80mm auto; margin: 0; }
      @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;900&display=swap');
      body {
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        font-size: ${language === 'ar' ? '14px' : '13px'};
        line-height: 1.3;
        color: #000;
        background: white;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        direction: ${language === 'ar' ? 'rtl' : 'ltr'};
        width: 80mm;
        margin: 0 auto;
        padding: ${language === 'ar' ? '2mm 6mm' : '2mm 4mm'};
        text-align: center;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        ${language === 'ar' ? 'transform: translateX(2mm);' : ''}
      }
      .thermal-invoice {
        width: 100%;
        text-align: center;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif" : "'Arial Black', sans-serif"};
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      /* Header Styles - Bigger and Clearer */
      .invoice-header {
        text-align: center;
        margin-bottom: 4mm;
        width: 100%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-logo {
        font-size: 24px;
        margin-bottom: 3mm;
        text-align: center;
        width: 100%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-logo img {
        width: 30mm;
        height: 12mm;
        object-fit: contain;
        margin: 0 auto 2mm auto;
        display: block;
        ${language === 'ar' ? 'margin-left: calc(50% + 2mm);' : ''}
      }
      .store-name {
        font-size: ${language === 'ar' ? '20px' : '18px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        margin-bottom: 2mm;
        text-align: center;
        width: 100%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .store-phone {
        font-size: ${language === 'ar' ? '16px' : '14px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        margin-bottom: 2mm;
        text-align: center;
        width: 100%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
        direction: ltr;
      }
      .store-address {
        font-size: ${language === 'ar' ? '14px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        margin-bottom: 3mm;
        text-align: center;
        word-wrap: break-word;
        width: 100%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      /* Title Styles - Bigger and Clearer */
      .invoice-title {
        font-size: ${language === 'ar' ? '18px' : '16px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        text-align: center;
        margin: 3mm auto;
        border: 2px solid #000;
        padding: 4mm;
        background: #f0f0f0;
        width: 95%;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: calc(2.5% + 2mm);' : ''}
      }
      .invoice-subtitle {
        font-size: ${language === 'ar' ? '14px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        text-align: center;
        margin-bottom: 3mm;
        font-family: ${language === 'ar' ? "'Cairo', 'Tahoma', 'Arial Unicode MS'" : "'Arial Black', 'Arial'"}, sans-serif;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }

      /* Separator Styles - Thicker */
      .separator { border-bottom: 3px solid #000; margin: 3mm 0; }
      .separator-dotted { border-bottom: 2px dashed #000; margin: 3mm 0; }

      /* Invoice Details - Bigger and Clearer */
      .invoice-details {
        margin: 3mm 0;
        width: 100%;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .detail-line {
        display: flex;
        justify-content: space-between;
        margin: 2mm 0;
        font-size: ${language === 'ar' ? '14px' : '13px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        width: 100%;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .detail-line .label {
        font-weight: ${language === 'ar' ? '700' : '900'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
      }
      .detail-line .value {
        text-align: ${isRTL ? 'left' : 'right'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
      }

      /* Products Table - Simplified 3-Column Layout */
      .products-table {
        margin: 3mm 0;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .table-header {
        display: flex;
        background: #000;
        color: white;
        padding: 2mm;
        font-size: 14px;
        font-weight: 900;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .table-row {
        display: flex;
        padding: 2mm;
        font-size: 14px;
        font-weight: bold;
        border-bottom: 2px dotted #000;
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .col-product { flex: 2.5; font-weight: bold; text-align: ${isRTL ? 'right' : 'left'}; }
      .col-total { flex: 2; text-align: ${isRTL ? 'right' : 'left'}; font-weight: bold; }

      /* Totals Section - Bigger and Clearer */
      .totals-section {
        margin: 3mm 0;
        width: 100%;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .total-line {
        display: flex;
        justify-content: space-between;
        margin: 2mm 0;
        font-size: ${language === 'ar' ? '15px' : '13px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        width: 100%;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .total-line .label {
        font-weight: ${language === 'ar' ? '700' : '900'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
      }
      .total-line .value {
        font-weight: ${language === 'ar' ? '700' : '900'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
      }
      .final-total {
        display: flex;
        justify-content: space-between;
        margin: 3mm auto;
        font-size: ${language === 'ar' ? '20px' : '18px'};
        font-weight: ${language === 'ar' ? '700' : '900'};
        border: 3px solid #000;
        padding: 5mm;
        background: #f0f0f0;
        width: 95%;
        text-align: center;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: calc(2.5% + 2mm);' : ''}
      }

      /* Footer Styles - Bigger and Clearer */
      .invoice-footer {
        text-align: center;
        margin-top: 4mm;
        font-size: ${language === 'ar' ? '14px' : '12px'};
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        width: 100%;
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .thank-you {
        font-weight: ${language === 'ar' ? '700' : '900'};
        margin-bottom: 3mm;
        font-size: ${language === 'ar' ? '16px' : '14px'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .developer-info {
        border-top: 2px solid #000;
        padding-top: 3mm;
        margin: 3mm 0;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
        ${language === 'ar' ? 'margin-left: 2mm;' : ''}
      }
      .print-time {
        font-size: ${language === 'ar' ? '12px' : '10px'};
        margin-top: 3mm;
        font-weight: ${language === 'ar' ? '600' : 'bold'};
        ${language === 'ar' ? 'font-family: "Cairo", "Tahoma", "Arial Unicode MS", sans-serif;' : ''}
      }

      @media print {
        body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
        .thermal-invoice { page-break-inside: avoid; }
      }
    `;
  }

  async generateSupplierTransactionContent(supplierName, transactions, totalCredit, options = {}, repairsData = []) {
    try {
      const { language = 'ar', storeSettings = {} } = options;
      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Supplier Transactions - ${supplierName}</title>
          <style>
            ${this.getThermalInvoiceStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-invoice">
            <!-- Header Section -->
            <div class="invoice-header">
              <div class="store-logo">
                ${storeSettings.storeLogo ?
                  `<img src="${storeSettings.storeLogo}" alt="Logo" onerror="this.innerHTML='🏪'" />` :
                  `🏪`
                }
              </div>
              <div class="store-name">${storeSettings.storeName || 'ICALDZ STORE'} ⭐</div>
              <div class="store-phone">📞 ${storeSettings.storePhone || '+213 555 123 456'}</div>
              <div class="store-address">${storeSettings.storeAddress || 'الجزائر العاصمة، الجزائر'}</div>
            </div>

            <div class="separator"></div>

            <!-- Invoice Title -->
            <div class="invoice-title">${t('supplierTransactions', 'معاملات المورد')}</div>
            <div class="invoice-subtitle">${supplierName}</div>

            <div class="separator"></div>

            <!-- Invoice Details -->
            <div class="invoice-details">
              <div class="detail-line">
                <span class="label">${t('supplierName', 'اسم المورد')}:</span>
                <span class="value">${supplierName}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('printedOn', 'طُبع في')}:</span>
                <span class="value">${new Date().toLocaleDateString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('time', 'الوقت')}:</span>
                <span class="value">${new Date().toLocaleTimeString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('totalTransactions', 'إجمالي المعاملات')}:</span>
                <span class="value">${transactions.length}</span>
              </div>
            </div>

            <div class="separator-dotted"></div>

            <!-- Transactions Table -->
            <div class="products-table">
              <div class="table-header" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                <span class="col-product">${language === 'ar' ? 'ا/ج&م' : language === 'fr' ? 'N/A&P' : 'N/A&P'}</span>
                <span class="col-price" style="padding: 0 15px;">${language === 'ar' ? 'السعر' : language === 'fr' ? 'PRIX' : 'PRICE'}</span>
                <span class="col-total">${language === 'ar' ? 'الحالة' : language === 'fr' ? 'STATUT' : 'STATUS'}</span>
              </div>
              ${transactions.map(transaction => {
                // Get device name and problem from repair data using repairId
                let deviceProblem = '';

                // First try to get from repair data using repairId
                if (transaction.repairId && repairsData.length > 0) {
                  const repair = repairsData.find(r => r.id === transaction.repairId);
                  if (repair) {
                    // Use device name and problem description from repair
                    const deviceName = repair.deviceName || '';
                    const problemDesc = repair.problemDescription || repair.problemType || '';
                    deviceProblem = problemDesc ? `${deviceName} - ${problemDesc}` : deviceName;
                  }
                }

                // Fallback to transaction partName if repair not found
                if (!deviceProblem && transaction.partName) {
                  // Try to extract device name from partName
                  const parts = transaction.partName.split(' - ');
                  if (parts.length > 1) {
                    // Format: "Parts for Device - Client"
                    const devicePart = parts[0].replace('Parts for ', '').replace('قطع لـ ', '');
                    deviceProblem = devicePart;
                  } else {
                    deviceProblem = transaction.partName;
                  }
                } else if (!deviceProblem && transaction.deviceName && transaction.problem) {
                  // Use device name and problem if available
                  deviceProblem = `${transaction.deviceName} - ${transaction.problem}`;
                } else if (!deviceProblem && transaction.deviceName) {
                  deviceProblem = transaction.deviceName;
                } else if (!deviceProblem) {
                  deviceProblem = t('repairParts', 'قطع الإصلاح');
                }

                return `
              <div class="table-row" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                <span class="col-product">${deviceProblem}</span>
                <span class="col-price" style="padding: 0 15px; text-align: center;">${Math.round(Math.abs(transaction.price || 0))}</span>
                <span class="col-total">${transaction.paid ?
                  (language === 'ar' ? 'مدفوع' : language === 'fr' ? 'PAYÉ' : 'PAID') :
                  (language === 'ar' ? 'معلق' : language === 'fr' ? 'EN ATTENTE' : 'PENDING')}</span>
              </div>
              `;
              }).join('')}
            </div>

            <div class="separator-dotted"></div>

            <!-- Totals Section -->
            <div class="totals-section">
              ${(() => {
                // Calculate totals properly (same logic as in the modal)
                const totalCredit = transactions
                  .filter(t => (t.status === 'credit' || !t.status) && parseFloat(t.price) > 0)
                  .reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0);

                const totalPaid = transactions
                  .filter(t => t.status === 'payment' || parseFloat(t.price) < 0)
                  .reduce((sum, t) => sum + Math.abs(parseFloat(t.price) || 0), 0) +
                  transactions
                  .filter(t => (t.status === 'credit' || !t.status))
                  .reduce((sum, t) => sum + (parseFloat(t.partialPayment) || 0), 0);

                const pendingAmount = totalCredit - totalPaid;

                return `
                  <div class="total-line" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                    <span class="label">${language === 'ar' ? 'المبلغ الإجمالي:' : language === 'fr' ? 'MONTANT TOTAL:' : 'TOTAL AMOUNT:'}</span>
                    <span class="value" style="padding: 0 15px;">${Math.round(totalCredit)}</span>
                  </div>
                  <div class="total-line" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                    <span class="label">${language === 'ar' ? 'المبلغ المدفوع:' : language === 'fr' ? 'MONTANT PAYÉ:' : 'PAID AMOUNT:'}</span>
                    <span class="value" style="padding: 0 15px;">${Math.round(totalPaid)}</span>
                  </div>
                  <div class="final-total" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                    <span class="label">${language === 'ar' ? 'الرصيد المتبقي:' : language === 'fr' ? 'CRÉDIT RESTANT:' : 'REMAINING CREDIT:'}</span>
                    <span class="value" style="padding: 0 15px;">${Math.round(pendingAmount)}</span>
                  </div>
                `;
              })()}
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="invoice-footer">
              <div class="thank-you">${t('thankYouBusiness', 'شكراً لتعاملكم معنا')}</div>
              <div class="developer-info">
                <div>🔧 ${t('repairSystem', 'نظام الإصلاح - ICALDZ')}</div>
              </div>
              <div class="print-time">${t('printedOn', 'طُبع في')}: ${new Date().toLocaleString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating supplier transaction content:', error);
      return null;
    }
  }

  async printSupplierTransactions(supplierName, transactions, totalCredit, options = {}, repairsData = []) {
    console.log('🖨️ RepairThermalPrinter.printSupplierTransactions called');
    console.log('📊 Supplier:', supplierName, 'Transactions:', transactions.length, 'Credit:', totalCredit);
    try {
      const content = await this.generateSupplierTransactionContent(supplierName, transactions, totalCredit, options, repairsData);
      if (!content) {
        console.error('❌ Failed to generate supplier transaction content');
        throw new Error('Failed to generate supplier transaction content');
      }
      console.log('✅ Content generated successfully');

      const printWindow = window.open('', '_blank', 'width=400,height=700');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error printing supplier transactions:', error);
      return false;
    }
  }

  async printRepairOrdersList(repairs, options = {}) {
    // Simplified version for now - just show a message
    console.log('Printing repair orders list:', repairs.length);
    return true;
  }
}

export default new RepairThermalPrinter();
