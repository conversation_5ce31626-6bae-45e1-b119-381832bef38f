import React, { useState, useEffect } from 'react';
import { useLanguage } from '../LanguageContext.jsx';
import { useAppState } from '../contexts/AppStateContext.jsx';
import QRCodeUtils from '../QRCodeUtils.js';
import RepairThermalPrinter from '../RepairThermalPrinterNew.js';

export default function RepairManagement() {
  const { t, currentLanguage } = useLanguage();
  const {
    repairsData,
    setRepairsData,
    suppliersPartsReparationData,
    setSuppliersPartsReparationData,
    currentUser,
    toasts,
    setToasts,
    suppliers,
    setSuppliers,
    customers
  } = useAppState();

  // Format price function with proper currency display
  const formatPrice = (price) => {
    const amount = Math.round(price || 0).toString();
    const currency = currentLanguage === 'ar' ? 'د.ج' : 'DZD';

    if (currentLanguage === 'ar') {
      return `${amount} ${currency}`;
    } else {
      return `${amount} ${currency}`;
    }
  };

  // Format problem type for clean display
  const formatProblemType = (problemType) => {
    if (!problemType) return '';

    // Handle camelCase and concatenated words
    const formatted = problemType
      // Add space before capital letters (camelCase)
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      // Add space before numbers
      .replace(/([a-zA-Z])(\d)/g, '$1 $2')
      // Handle common abbreviations and make them uppercase
      .replace(/\blcd\b/gi, 'LCD')
      .replace(/\boled\b/gi, 'OLED')
      .replace(/\bram\b/gi, 'RAM')
      .replace(/\bcpu\b/gi, 'CPU')
      .replace(/\bgpu\b/gi, 'GPU')
      .replace(/\busb\b/gi, 'USB')
      .replace(/\bwifi\b/gi, 'WiFi')
      // Capitalize first letter of each word
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');

    return formatted;
  };

  // Local state for repair management
  const [showNewRepairModal, setShowNewRepairModal] = useState(false);
  const [showRepairCompletedModal, setShowRepairCompletedModal] = useState(false);
  const [showClientPickupModal, setShowClientPickupModal] = useState(false);
  const [showRepairDetailsModal, setShowRepairDetailsModal] = useState(false);
  const [repairFilter, setRepairFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRepairs, setSelectedRepairs] = useState([]);
  const [selectedRepair, setSelectedRepair] = useState(null);
  const [selectedRepairForCompletion, setSelectedRepairForCompletion] = useState(null);

  // Suppliers table modal states (unique naming to avoid conflicts)
  const [showSupplierpartsPayment, setShowSupplierpartsPayment] = useState(false);
  const [showSupplierTransactionparts, setShowSupplierTransactionparts] = useState(false);
  const [showSupplierpartsEdit, setShowSupplierpartsEdit] = useState(false);
  const [showSupplierpartsDelete, setShowSupplierpartsDelete] = useState(false);
  const [selectedSupplierparts, setSelectedSupplierparts] = useState(null);
  const [paymentAmountparts, setPaymentAmountparts] = useState('');
  const [deletePasscodeparts, setDeletePasscodeparts] = useState('');
  const [transactionpartsSearchFilter, setTransactionpartsSearchFilter] = useState('');

  // New repair form state
  const [newRepairForm, setNewRepairForm] = useState({
    clientName: '',
    clientPhone: '',
    deviceName: '',
    deviceType: '',
    problemType: '',
    problemDescription: '',
    repairPrice: '',
    partialPayment: '',
    paymentStatus: 'unpaid',
    depositDate: new Date().toISOString().split('T')[0],
    depositTime: new Date().toTimeString().split(' ')[0].substring(0, 5),
    repairBarcode: '',
    remarks: ''
  });

  // Repair completion form state
  const [repairCompletionForm, setRepairCompletionForm] = useState({
    partsPrice: '',
    supplierName: '',
    remarks: '',
    failureRemarks: '',
    verificationPrice: ''
  });

  // Custom problems state
  const [customProblems, setCustomProblems] = useState([]);
  const [showProblemManagerModal, setShowProblemManagerModal] = useState(false);
  const [editingProblem, setEditingProblem] = useState(null);
  const [newProblemForm, setNewProblemForm] = useState({ name: '', description: '' });

  // QR Code state
  const [repairQRCodes, setRepairQRCodes] = useState({});
  const [showQRModal, setShowQRModal] = useState(false);
  const [selectedQRCode, setSelectedQRCode] = useState(null);

  // Add Supplier Parts state
  const [showAddSupplierModal, setShowAddSupplierModal] = useState(false);
  const [newSupplierForm, setNewSupplierForm] = useState({
    supplierName: '',
    partName: '',
    partDescription: '',
    price: '',
    quantity: '',
    repairId: '',
    paid: false
  });

  // Toast notification function
  const showToast = (message, type = 'success', duration = 3000) => {
    const id = Date.now() + Math.random();
    const newToast = { id, message, type, duration };
    setToasts(prev => [...prev, newToast]);

    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, duration);
  };

  // Helper functions for suppliers table functionality
  const getSupplierTransactions = (supplierName) => {
    console.log('🔍 getSupplierTransactions called for:', supplierName);
    console.log('📊 Available data:', suppliersPartsReparationData.length, 'transactions');
    console.log('📋 All suppliers in data:', [...new Set(suppliersPartsReparationData.map(t => t.supplierName))]);

    const transactions = suppliersPartsReparationData.filter(
      transaction => transaction.supplierName === supplierName
    );

    console.log('✅ Found transactions:', transactions.length);
    console.log('📄 Transaction details:', transactions.map(t => ({
      id: t.id,
      repairId: t.repairId,
      partName: t.partName,
      price: t.price,
      status: t.status,
      paid: t.paid,
      date: t.date
    })));

    return transactions;
  };

  const getSupplierTotalCredit = (supplierName) => {
    console.log('💰 getSupplierTotalCredit called for:', supplierName);

    const transactions = getSupplierTransactions(supplierName);
    const totalCredit = transactions
      .filter(transaction => (transaction.status === 'credit' || !transaction.status) && !transaction.paid)
      .reduce((total, transaction) => {
        const transactionAmount = parseFloat(transaction.price) || 0;
        const partialPayment = parseFloat(transaction.partialPayment) || 0;
        return total + (transactionAmount - partialPayment);
      }, 0);

    console.log('💰 Total credit calculated:', totalCredit);
    return totalCredit;
  };

  const saveSuppliersPartsReparationData = (data) => {
    setSuppliersPartsReparationData(data);
    localStorage.setItem('icaldz-suppliers-parts-reparation', JSON.stringify(data));
  };

  // Add new supplier
  const handleAddSupplierPart = () => {
    if (!newSupplierForm.supplierName) {
      showToast(
        currentLanguage === 'ar' ? 'يرجى ملء اسم المورد' :
        currentLanguage === 'fr' ? 'Veuillez remplir le nom du fournisseur' :
        'Please fill supplier name',
        'error'
      );
      return;
    }

    const newSupplier = {
      id: Date.now().toString(),
      name: newSupplierForm.supplierName,
      phone: newSupplierForm.phone || '',
      email: newSupplierForm.email || '',
      address: newSupplierForm.address || '',
      city: newSupplierForm.city || '',
      date: new Date().toISOString().split('T')[0],
      timestamp: Date.now()
    };

    const updatedSuppliers = [...suppliers, newSupplier];
    setSuppliers(updatedSuppliers);
    localStorage.setItem('icaldz-suppliers', JSON.stringify(updatedSuppliers));

    // Reset form
    setNewSupplierForm({
      supplierName: '',
      phone: '',
      email: '',
      address: '',
      city: ''
    });

    setShowAddSupplierModal(false);
    showToast(
      `✅ ${currentLanguage === 'ar' ? 'تم إضافة المورد بنجاح' :
            currentLanguage === 'fr' ? 'Fournisseur ajouté avec succès' :
            'Supplier added successfully'}`,
      'success'
    );
  };

  // Close add supplier modal
  const closeAddSupplierModal = () => {
    setShowAddSupplierModal(false);
    setNewSupplierForm({
      supplierName: '',
      phone: '',
      email: '',
      address: '',
      city: ''
    });
  };

  // Close add supplier profile modal
  const closeAddSupplierProfileModal = () => {
    setShowAddSupplierProfileModal(false);
    setNewSupplierProfileForm({
      supplierName: '',
      businessType: '',
      phone: '',
      email: '',
      address: '',
      city: '',
      state: '',
      notes: ''
    });
  };

  // Thermal printing function for supplier transactions
  const printSupplierTransactions = async (supplierName) => {
    console.log('🖨️ printSupplierTransactions called for:', supplierName);
    try {
      const transactions = getSupplierTransactions(supplierName);
      const totalCredit = getSupplierTotalCredit(supplierName);

      console.log('📊 Printing data:', {
        supplier: supplierName,
        transactions: transactions.length,
        totalCredit,
        transactionData: transactions
      });

      // Calculate payment summary for printing
      const totalPaid = transactions
        .filter(t => t.status === 'payment' || parseFloat(t.price) < 0)
        .reduce((sum, t) => sum + Math.abs(parseFloat(t.price) || 0), 0) +
        transactions
        .filter(t => (t.status === 'credit' || !t.status))
        .reduce((sum, t) => sum + (parseFloat(t.partialPayment) || 0), 0);

      const totalAmount = transactions
        .filter(t => (t.status === 'credit' || !t.status) && parseFloat(t.price) > 0)
        .reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0);

      const success = await RepairThermalPrinter.printSupplierTransactions(
        supplierName,
        transactions,
        totalCredit,
        {
          language: currentLanguage,
          showToast: showToast,
          storeSettings: {
            storeName: 'iCALDZ STORE',
            storePhone: '+213 555 123 456',
            storeAddress: currentLanguage === 'ar' ? 'الجزائر العاصمة، الجزائر' : 'Alger, Algérie'
          },
          summary: {
            totalAmount,
            totalPaid,
            pendingAmount: totalAmount - totalPaid
          }
        },
        repairsData
      );

      if (success) {
        showToast(`🖨️ ${currentLanguage === 'ar' ? 'تم طباعة تقرير المورد' : currentLanguage === 'fr' ? 'Rapport fournisseur imprimé' : 'Supplier report printed'} ${supplierName}`, 'success', 2000);
      } else {
        showToast(`❌ ${currentLanguage === 'ar' ? 'خطأ في طباعة تقرير المورد' : currentLanguage === 'fr' ? 'Erreur d\'impression du rapport' : 'Error printing supplier report'}`, 'error', 3000);
      }
    } catch (error) {
      console.error('Error printing supplier transactions:', error);
      showToast(`❌ ${currentLanguage === 'ar' ? 'خطأ في طباعة تقرير المورد' : currentLanguage === 'fr' ? 'Erreur d\'impression du rapport' : 'Error printing supplier report'}`, 'error', 3000);
    }
  };

  // Load repairs from localStorage
  const loadRepairs = () => {
    const savedRepairsData = localStorage.getItem('icaldz-repairs');
    return savedRepairsData ? JSON.parse(savedRepairsData) : [];
  };

  // Save repairs to localStorage
  const saveRepairs = (newRepairsData) => {
    try {
      localStorage.setItem('icaldz-repairs', JSON.stringify(newRepairsData));
      setRepairsData(newRepairsData);
    } catch (error) {
      console.error('❌ Error saving repairs:', error);
      showToast('❌ خطأ في حفظ الإصلاح', 'error', 3000);
    }
  };

  // Generate repair barcode
  const generateRepairBarcode = () => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `REP${timestamp.slice(-6)}${random}`;
  };

  // Load custom problems from localStorage
  const loadCustomProblems = () => {
    const savedProblems = localStorage.getItem('icaldz-custom-problems');
    return savedProblems ? JSON.parse(savedProblems) : [];
  };

  // Save custom problems to localStorage
  const saveCustomProblems = (problems) => {
    localStorage.setItem('icaldz-custom-problems', JSON.stringify(problems));
    setCustomProblems(problems);
  };

  // Load data on component mount
  useEffect(() => {
    const loadedRepairs = loadRepairs();
    if (loadedRepairs.length > 0) {
      setRepairsData(loadedRepairs);
    }

    const loadedProblems = loadCustomProblems();
    if (loadedProblems.length > 0) {
      setCustomProblems(loadedProblems);
    }
  }, [setRepairsData]);

  // Create new repair order
  const createRepairOrder = async () => {
    if (!newRepairForm.clientName.trim()) {
      showToast(t('pleaseEnterClientName', 'يرجى إدخال اسم العميل'), 'error');
      return;
    }

    if (!newRepairForm.deviceName.trim()) {
      showToast(t('pleaseEnterDeviceName', 'يرجى إدخال اسم الجهاز'), 'error');
      return;
    }

    if (!newRepairForm.deviceType) {
      showToast(t('pleaseSelectDeviceType', 'يرجى اختيار نوع الجهاز'), 'error');
      return;
    }

    if (!newRepairForm.problemType) {
      showToast(t('pleaseSelectProblemType', 'يرجى اختيار نوع المشكلة'), 'error');
      return;
    }

    if (!newRepairForm.repairPrice || parseFloat(newRepairForm.repairPrice) <= 0) {
      showToast(t('pleaseEnterValidRepairPrice', 'يرجى إدخال سعر إصلاح صحيح'), 'error');
      return;
    }

    // Check if we're editing an existing repair or creating a new one
    if (selectedRepair) {
      // Edit existing repair - update the existing repair in the array
      const updatedRepairs = repairsData.map(repair => {
        if (repair.id === selectedRepair.id) {
          return {
            ...repair,
            ...newRepairForm,
            updatedAt: new Date().toISOString()
          };
        }
        return repair;
      });

      saveRepairs(updatedRepairs);
      showToast(t('repairUpdated', 'تم تحديث الإصلاح بنجاح'), 'success');
    } else {
      // Create new repair
      const repairBarcode = generateRepairBarcode();
      const newRepair = {
        id: 'REP-' + Date.now(),
        ...newRepairForm,
        repairBarcode: repairBarcode,
        status: 'inProgress', // Start with in progress status for repair workflow
        partsPrice: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const updatedRepairs = [newRepair, ...repairsData];
      saveRepairs(updatedRepairs);

      // Automatically print repair ticket after creating new repair
      try {
        await RepairThermalPrinter.openPrintWindow(newRepair, {
          language: currentLanguage,
          showToast: showToast
        });
        showToast(
          `🖨️ ${t('repairOrderCreated', 'تم إنشاء أمر الإصلاح بنجاح')} - ${t('printingTicket', 'طباعة التذكرة')}`,
          'success'
        );
      } catch (error) {
        console.error('Error printing repair ticket:', error);
        showToast(t('repairOrderCreated', 'تم إنشاء أمر الإصلاح بنجاح'), 'success');
        showToast('❌ خطأ في طباعة التذكرة', 'error', 3000);
      }
    }

    // Reset form and close modal
    closeNewRepairModal();
  };

  // Close repair modals with proper state cleanup
  const closeNewRepairModal = () => {
    setShowNewRepairModal(false);
    setSelectedRepair(null); // Reset selected repair for edit mode
    setNewRepairForm({
      clientName: '',
      clientPhone: '',
      deviceName: '',
      deviceType: '',
      problemType: '',
      problemDescription: '',
      repairPrice: '',
      partialPayment: '',
      paymentStatus: 'unpaid',
      depositDate: new Date().toISOString().split('T')[0],
      depositTime: new Date().toTimeString().split(' ')[0].substring(0, 5),
      repairBarcode: '',
      remarks: ''
    });
  };

  const closeRepairCompletedModal = () => {
    setShowRepairCompletedModal(false);
    setSelectedRepairForCompletion(null);
    setRepairCompletionForm({
      partsPrice: '',
      supplierName: '',
      remarks: '',
      verificationPrice: '',
      failureRemarks: ''
    });
  };

  const closeClientPickupModal = () => {
    setShowClientPickupModal(false);
    setSelectedRepair(null);
  };

  // Mark repair as completed (Success - goes to waitingForClient with green background)
  const markRepairCompleted = (repairId, partsPrice, remarks, supplierName) => {
    const updatedRepairs = repairsData.map(repair => {
      if (repair.id === repairId) {
        return {
          ...repair,
          status: 'waitingForClient', // Success path - green background
          partsPrice: parseFloat(partsPrice) || 0,
          remarks: remarks,
          supplierName: supplierName,
          completedAt: new Date().toISOString(),
          isSuccessfulRepair: true // Flag to indicate successful repair
        };
      }
      return repair;
    });

    saveRepairs(updatedRepairs);

    // Add to supplier parts reparation if parts were used
    if (partsPrice && parseFloat(partsPrice) > 0 && supplierName) {
      const newTransaction = {
        id: 'SPR-' + Date.now(),
        repairId: repairId,
        supplierName: supplierName,
        partName: currentLanguage === 'ar' ? 'قطع غيار' : currentLanguage === 'fr' ? 'Pièces de rechange' : 'Spare parts',
        price: parseFloat(partsPrice),
        paid: false,
        partialPayment: 0,
        paidAmount: 0,
        status: 'credit',
        date: new Date().toISOString(),
        paidDate: null,
        lastPaymentDate: null,
        remarks: remarks || ''
      };

      const updatedSupplierParts = [...suppliersPartsReparationData, newTransaction];
      setSuppliersPartsReparationData(updatedSupplierParts);
      localStorage.setItem('icaldz-suppliers-parts-reparation', JSON.stringify(updatedSupplierParts));
    }

    showToast(t('repairMarkedCompleted', 'تم تحديد الإصلاح كمكتمل - في انتظار العميل'), 'success');
    closeRepairCompletedModal();
  };

  // Mark repair as failed (Not Success - goes to enAttenteClient with red background)
  const markRepairFailed = (repairId, failureRemarks, verificationPrice) => {
    const updatedRepairs = repairsData.map(repair => {
      if (repair.id === repairId) {
        return {
          ...repair,
          status: 'enAttenteClient', // Failed path - red background
          failureRemarks: failureRemarks,
          verificationPrice: parseFloat(verificationPrice) || 0,
          failedAt: new Date().toISOString(),
          isFailedRepair: true // Flag to indicate failed repair
        };
      }
      return repair;
    });

    saveRepairs(updatedRepairs);
    showToast(t('repairMarkedFailed', 'تم تحديد الإصلاح كفاشل - في انتظار العميل'), 'warning');
    closeRepairCompletedModal();
  };

  // Add custom problem
  const addCustomProblem = (problemData) => {
    const newProblem = {
      id: Date.now().toString(),
      name: problemData.name,
      description: problemData.description,
      createdAt: new Date().toISOString()
    };

    const updatedProblems = [...customProblems, newProblem];
    saveCustomProblems(updatedProblems);
    return newProblem;
  };

  // Edit repair order (admin only)
  const editRepairOrder = (repair) => {
    if (currentUser.role !== 'مدير' && currentUser.role !== 'admin') {
      showToast('❌ غير مسموح - المدير فقط يمكنه التعديل', 'error');
      return;
    }

    setSelectedRepair(repair);
    setNewRepairForm({...repair});
    setShowNewRepairModal(true);
  };

  // Generate QR code for repair
  const generateQRCodeForRepair = async (repair) => {
    try {
      const qrCode = await QRCodeUtils.generateRepairQRCode(repair);
      if (qrCode) {
        setRepairQRCodes(prev => ({
          ...prev,
          [repair.id]: qrCode
        }));
        return qrCode;
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      showToast('❌ خطأ في إنشاء رمز QR', 'error');
    }
    return null;
  };

  // Show QR code modal
  const showQRCodeModal = async (repair) => {
    let qrCode = repairQRCodes[repair.id];

    if (!qrCode) {
      qrCode = await generateQRCodeForRepair(repair);
    }

    if (qrCode) {
      setSelectedQRCode({
        repair: repair,
        qrCode: qrCode
      });
      setShowQRModal(true);
    }
  };

  // Print repair ticket with QR code
  const printRepairTicket = async (repair) => {
    try {
      const ticketData = await QRCodeUtils.generatePrintableTicket(repair, {
        format: 'thermal',
        language: currentLanguage
      });

      if (ticketData) {
        // Here you would integrate with actual printer
        console.log('Printing repair ticket:', ticketData);
        showToast(`🖨️ ${t('printingRepairTicket', 'طباعة تذكرة الإصلاح')} ${repair.id}`, 'success');
      }
    } catch (error) {
      console.error('Error printing repair ticket:', error);
      showToast('❌ خطأ في طباعة التذكرة', 'error');
    }
  };

  // Translate repair status based on language
  const translateRepairStatus = (status) => {
    const statusTranslations = {
      // Current system status values
      'inProgress': {
        ar: 'قيد الإصلاح',
        fr: 'En Cours',
        en: 'In Progress'
      },
      'En Cours': {
        ar: 'قيد الإصلاح',
        fr: 'En Cours',
        en: 'In Progress'
      },
      'waitingForClient': {
        ar: 'في انتظار العميل',
        fr: 'En Attente Client',
        en: 'Waiting for Client'
      },
      'enAttenteClient': {
        ar: 'في انتظار العميل',
        fr: 'En Attente Client',
        en: 'Waiting for Client'
      },
      'done': {
        ar: 'مكتمل',
        fr: 'Terminé',
        en: 'Completed'
      },
      'echec': {
        ar: 'فشل',
        fr: 'Échec',
        en: 'Failed'
      },
      // Legacy Arabic status values (for backward compatibility)
      'في الانتظار': {
        ar: 'في الانتظار',
        fr: 'En Attente',
        en: 'Pending'
      },
      'قيد الإصلاح': {
        ar: 'قيد الإصلاح',
        fr: 'En Cours',
        en: 'In Progress'
      },
      'مكتمل': {
        ar: 'مكتمل',
        fr: 'Terminé',
        en: 'Completed'
      },
      'في انتظار العميل': {
        ar: 'في انتظار العميل',
        fr: 'En Attente Client',
        en: 'Waiting for Client'
      },
      'تم التسليم': {
        ar: 'تم التسليم',
        fr: 'Livré',
        en: 'Delivered'
      },
      'فشل': {
        ar: 'فشل',
        fr: 'Échec',
        en: 'Failed'
      }
    };

    return statusTranslations[status]?.[currentLanguage] || status;
  };

  // Enhanced search function with flexible matching
  const enhancedSearch = (text, searchTerm) => {
    if (!text || !searchTerm) return false;

    const normalizedText = text.toLowerCase().replace(/\s+/g, '');
    const normalizedSearch = searchTerm.toLowerCase().replace(/\s+/g, '');

    // Check both original and normalized versions
    return text.toLowerCase().includes(searchTerm.toLowerCase()) ||
           normalizedText.includes(normalizedSearch);
  };

  // Filter repairs based on search and status
  const filteredRepairs = repairsData.filter(repair => {
    if (!repairFilter.trim()) {
      const matchesStatus = statusFilter === 'all' || repair.status === statusFilter;
      return matchesStatus;
    }

    const searchTerm = repairFilter.trim();

    const matchesSearch =
      // N° de Réparation (Repair ID)
      repair.id?.toString().includes(searchTerm) ||
      repair.repairBarcode?.toLowerCase().includes(searchTerm.toLowerCase()) ||

      // Nom Client (Client Name)
      enhancedSearch(repair.clientName, searchTerm) ||

      // Nom de l'Appareil (Device Name)
      enhancedSearch(repair.deviceName, searchTerm) ||

      // Problème (Problem Type) - Enhanced search with space handling
      enhancedSearch(repair.problemType, searchTerm) ||
      enhancedSearch(formatProblemType(repair.problemType), searchTerm) ||

      // Additional fields for comprehensive search
      enhancedSearch(repair.problemDescription, searchTerm) ||
      enhancedSearch(repair.clientPhone, searchTerm) ||
      enhancedSearch(repair.deviceType, searchTerm);

    const matchesStatus = statusFilter === 'all' || repair.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Generate advanced repair report
  const generateAdvancedRepairReport = (format) => {
    showToast(`📊 ${t('generatingRepairReport', 'إنشاء تقرير الإصلاحات')} (${format.toUpperCase()})`, 'info', 2000);
  };

  // Print thermal repair report
  const printRepairManagementThermal = () => {
    showToast(`🖨️ ${t('printingThermalReport', 'طباعة تقرير حراري')}`, 'success', 2000);
  };

  // Handle repair status update
  const updateRepairStatus = (repairId, newStatus) => {
    const updatedRepairs = repairsData.map(repair =>
      repair.id === repairId ? { ...repair, status: newStatus } : repair
    );
    setRepairsData(updatedRepairs);
    showToast(`✅ ${t('repairStatusUpdated', 'تم تحديث حالة الإصلاح')}`, 'success', 2000);
  };

  // Process client pickup - exact implementation from App_old.jsx
  const processPickup = (repair, finalPrice) => {
    console.log('🔄 processPickup called for repair:', repair.id, 'status:', repair.status);

    if (!repair) {
      showToast(t('repairNotFound', 'Repair not found'), 'error');
      return;
    }

    // Use provided final price or determine default based on repair status
    let defaultPrice;
    if (repair.isFailedRepair || repair.status === 'enAttenteClient') {
      // For failed repairs, use verification price if available, otherwise 0
      defaultPrice = repair.verificationPrice || 0;
    } else {
      // For successful repairs, use repair price
      defaultPrice = parseFloat(repair.repairPrice || 0);
    }

    const finalTotalPrice = finalPrice !== undefined ? parseFloat(finalPrice) :
                           (repair.totalPrice || defaultPrice);

    const updatedRepairs = repairsData.map(r => {
      if (r.id === repair.id) {
        // Check if this is a failed repair (exact App_old.jsx logic)
        const isFailed = r.isFailedRepair || r.status === 'enAttenteClient';

        return {
          ...r,
          status: isFailed ? 'echec' : 'done', // Failed repairs become 'echec', successful ones become 'done'
          paymentStatus: 'paid', // Automatically set to paid when repair is completed
          finalPrice: finalTotalPrice, // Final price paid by client
          totalPrice: finalTotalPrice, // Update total price if needed
          pickedUpAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      }
      return r;
    });

    saveRepairs(updatedRepairs);
    closeClientPickupModal();
    showToast(t('clientPickupCompleted', 'Client pickup completed - Status: Done'), 'success');
  };

  // Delete repair
  const deleteRepair = (repairId) => {
    if (window.confirm(t('confirmDeleteRepair', 'هل أنت متأكد من حذف هذا الإصلاح؟'))) {
      const updatedRepairs = repairsData.filter(repair => repair.id !== repairId);
      setRepairsData(updatedRepairs);
      showToast(`🗑️ ${t('repairDeleted', 'تم حذف الإصلاح')}`, 'success', 2000);
    }
  };

  return (
    <div className={`repairs-page-container lang-${currentLanguage}`}>
      <div className={`repairs-page lang-${currentLanguage}`}>
        <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`} style={{
          direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
          textAlign: currentLanguage === 'ar' ? 'right' : 'left'
        }}>
          <div className="page-title-section">
            <h1 style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>🛠️ {t('repairManagement', 'إدارة الإصلاحات')}</h1>
          </div>
          <div className="page-description-section">
            <p style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}>{t('manageRepairOrders', 'إدارة أوامر الإصلاح وتتبع حالة الأجهزة')}</p>
          </div>
        </div>

      {/* Modern Three Big Action Buttons */}
      <div className={`modern-repair-actions-row ${currentLanguage !== 'ar' ? 'actions-ltr' : 'actions-rtl'}`}>
        <button
          className="big-action-btn nouveau-bon-btn"
          onClick={() => setShowNewRepairModal(true)}
          style={{
            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
          }}
        >
          <div className="big-btn-icon">📝</div>
          <div className="big-btn-content">
            <h2>
              {currentLanguage === 'ar' ? 'إنشاء أمر إصلاح جديد' :
               currentLanguage === 'fr' ? 'Nouvel ordre de réparation' :
               'Create New Repair Order'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'معلومات العميل والجهاز' :
               currentLanguage === 'fr' ? 'Informations Client et Appareil' :
               'Client and Device Information'}
            </p>
          </div>
        </button>

        <button
          className="big-action-btn reparation-terminee-btn"
          onClick={() => setShowRepairCompletedModal(true)}
          style={{
            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
          }}
        >
          <div className="big-btn-icon">🔧</div>
          <div className="big-btn-content">
            <h2>
              {currentLanguage === 'ar' ? 'إصلاح مكتمل' :
               currentLanguage === 'fr' ? 'Réparation Terminée' :
               'Repair Completed'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'تحديد الإصلاح كمكتمل' :
               currentLanguage === 'fr' ? 'Marquer la réparation comme terminée' :
               'Mark repair as completed'}
            </p>
          </div>
        </button>

        <button
          className="big-action-btn waiting-client-btn"
          onClick={() => setShowClientPickupModal(true)}
          style={{
            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
          }}
        >
          <div className="big-btn-icon">📱</div>
          <div className="big-btn-content">
            <h2>
              {currentLanguage === 'ar' ? 'في انتظار العميل' :
               currentLanguage === 'fr' ? 'En Attente du Client' :
               'Waiting for Client'}
            </h2>
            <p>
              {currentLanguage === 'ar' ? 'معالجة استلام العميل' :
               currentLanguage === 'fr' ? 'Traiter la récupération client' :
               'Process client pickup'}
            </p>
          </div>
        </button>
      </div>

      {/* Modern Repair Orders Header */}
      <div className={`repair-orders-header ${currentLanguage !== 'ar' ? 'header-ltr' : 'header-rtl'}`} style={{
        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        <div className="orders-title-section" style={{
          order: currentLanguage === 'ar' ? 2 : 1
        }}>
          <h1 className="orders-main-title" style={{
            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
            direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
            textAlign: currentLanguage === 'ar' ? 'right' : 'left'
          }}>
            📋 {currentLanguage === 'ar' ? 'أوامر الإصلاح' :
                currentLanguage === 'fr' ? 'Ordres de Réparation' :
                'Repair Orders'}
          </h1>
          <p className="orders-subtitle" style={{
            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
            direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
            textAlign: currentLanguage === 'ar' ? 'right' : 'left'
          }}>
            {currentLanguage === 'ar' ? 'إدارة وتتبع جميع أوامر الإصلاح' :
             currentLanguage === 'fr' ? 'Gérer et suivre tous les ordres de réparation' :
             'Manage and track all repair orders'}
          </p>
        </div>
        <div className="header-actions-section" style={{
          display: 'flex',
          gap: '0.5rem',
          flexWrap: 'wrap',
          direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
          order: currentLanguage === 'ar' ? 1 : 2
        }}>
          <button
            className="btn-modern btn-info"
            onClick={() => generateAdvancedRepairReport('a4')}
            title={currentLanguage === 'ar' ? 'تقرير إدارة الإصلاحات A4' :
                   currentLanguage === 'fr' ? 'Rapport Gestion des Réparations A4' :
                   'Repair Management Report A4'}
            style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}
          >
            <span className="btn-icon">📊</span>
            <span className="btn-text">
              {currentLanguage === 'ar' ? 'تقرير A4' :
               currentLanguage === 'fr' ? 'Rapport A4' :
               'Report A4'}
            </span>
          </button>
          <button
            className="btn-modern btn-secondary"
            onClick={() => printRepairManagementThermal()}
            title={currentLanguage === 'ar' ? 'تقرير إدارة الإصلاحات حراري' :
                   currentLanguage === 'fr' ? 'Rapport Gestion des Réparations Thermique' :
                   'Repair Management Report Thermal'}
            style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
            }}
          >
            <span className="btn-icon">🖨️</span>
            <span className="btn-text">
              {currentLanguage === 'ar' ? 'تقرير حراري' :
               currentLanguage === 'fr' ? 'Rapport Thermique' :
               'Report Thermal'}
            </span>
          </button>
        </div>
      </div>

      {/* Modern Status Containers in One Row */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        marginBottom: '2rem',
        flexWrap: 'wrap',
        justifyContent: 'space-between'
      }}>
        <div className="status-item" style={{
          textAlign: 'center',
          padding: '0.5rem 1rem',
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #28a745, #20c997)',
          color: 'white',
          minWidth: '120px',
          flex: '1'
        }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
            {repairsData.filter(r => r.status === 'done').length}
          </div>
          <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
            {currentLanguage === 'ar' ? 'مكتملة' :
             currentLanguage === 'fr' ? 'Terminées' :
             'Completed'}
          </div>
        </div>
        <div className="status-item" style={{
          textAlign: 'center',
          padding: '0.5rem 1rem',
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #007bff, #0056b3)',
          color: 'white',
          minWidth: '120px',
          flex: '1'
        }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
            {repairsData.filter(r => r.status === 'inProgress' || r.status === 'En Cours').length}
          </div>
          <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
            {currentLanguage === 'ar' ? 'قيد المعالجة' :
             currentLanguage === 'fr' ? 'En Cours' :
             'In Progress'}
          </div>
        </div>
        <div className="status-item" style={{
          textAlign: 'center',
          padding: '0.5rem 1rem',
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #ffc107, #e0a800)',
          color: 'white',
          minWidth: '120px',
          flex: '1'
        }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
            {repairsData.filter(r => r.status === 'waitingForClient' || r.status === 'enAttenteClient').length}
          </div>
          <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
            {currentLanguage === 'ar' ? 'في الانتظار' :
             currentLanguage === 'fr' ? 'En Attente' :
             'Waiting'}
          </div>
        </div>
        <div className="status-item" style={{
          textAlign: 'center',
          padding: '0.5rem 1rem',
          borderRadius: '8px',
          background: 'linear-gradient(135deg, #dc3545, #c82333)',
          color: 'white',
          minWidth: '120px',
          flex: '1'
        }}>
          <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
            {repairsData.filter(r => r.status === 'echec').length}
          </div>
          <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
            {currentLanguage === 'ar' ? 'فشل' :
             currentLanguage === 'fr' ? 'Échec' :
             'Failed'}
          </div>
        </div>
      </div>

      {/* Modern Filters and Status Row */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        padding: '1rem',
        background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
        borderRadius: '12px',
        marginBottom: '1rem',
        border: '1px solid #dee2e6',
        flexWrap: 'wrap'
      }}>
        {/* Search Input */}
        <div style={{ flex: '1', minWidth: '300px', position: 'relative' }}>
          <input
            type="text"
            placeholder={`🔍 ${currentLanguage === 'ar' ? 'البحث في الإصلاحات...' : currentLanguage === 'fr' ? 'Rechercher réparations...' : 'Search repairs...'}`}
            value={repairFilter}
            onChange={(e) => setRepairFilter(e.target.value)}
            style={{
              width: '100%',
              padding: '12px 16px',
              border: '2px solid #29B6F6',
              borderRadius: '25px',
              fontSize: '1rem',
              outline: 'none',
              transition: 'all 0.3s ease',
              background: 'white',
              boxShadow: '0 2px 8px rgba(41, 182, 246, 0.1)'
            }}
          />
          {repairFilter && (
            <button
              onClick={() => setRepairFilter('')}
              style={{
                position: 'absolute',
                right: '12px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: '#29B6F6',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              ✕
            </button>
          )}
        </div>

        {/* Status Filter */}
        <div style={{ minWidth: '200px' }}>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            style={{
              padding: '12px 16px',
              border: '2px solid #29B6F6',
              borderRadius: '25px',
              fontSize: '1rem',
              outline: 'none',
              background: 'white',
              cursor: 'pointer',
              minWidth: '200px',
              boxShadow: '0 2px 8px rgba(41, 182, 246, 0.1)'
            }}
          >
            <option value="all">{currentLanguage === 'ar' ? 'جميع الحالات' : currentLanguage === 'fr' ? 'Tous les statuts' : 'All Statuses'}</option>
            <option value="inProgress">{currentLanguage === 'ar' ? 'قيد الإصلاح' : currentLanguage === 'fr' ? 'En Cours' : 'In Progress'}</option>
            <option value="waitingForClient">{currentLanguage === 'ar' ? 'في انتظار العميل' : currentLanguage === 'fr' ? 'En Attente Client' : 'Waiting for Client'}</option>
            <option value="enAttenteClient">{currentLanguage === 'ar' ? 'في انتظار العميل (فاشل)' : currentLanguage === 'fr' ? 'En Attente Client (Échec)' : 'Waiting for Client (Failed)'}</option>
            <option value="done">{currentLanguage === 'ar' ? 'مكتمل' : currentLanguage === 'fr' ? 'Terminé' : 'Completed'}</option>
            <option value="echec">{currentLanguage === 'ar' ? 'فشل' : currentLanguage === 'fr' ? 'Échec' : 'Failed'}</option>
          </select>
        </div>

        {/* Status Badges */}
        <div style={{
          display: 'flex',
          gap: '0.5rem',
          flexWrap: 'wrap',
          alignItems: 'center'
        }}>
          <span style={{
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '0.85rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            ✅ {repairsData.filter(r => r.status === 'done').length}
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #29B6F6, #1E88E5)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '0.85rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            🔧 {repairsData.filter(r => r.status === 'inProgress' || r.status === 'En Cours').length}
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '0.85rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            🟢 {repairsData.filter(r => (r.status === 'waitingForClient' || r.status === 'enAttenteClient') && !r.isFailedRepair).length}
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #ff9800, #f57c00)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '0.85rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            🟠 {repairsData.filter(r => (r.status === 'waitingForClient' || r.status === 'enAttenteClient') && r.isFailedRepair).length}
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '20px',
            fontSize: '0.85rem',
            fontWeight: '500',
            display: 'flex',
            alignItems: 'center',
            gap: '4px'
          }}>
            ❌ {repairsData.filter(r => r.status === 'echec' || (r.status === 'done' && r.isFailedRepair)).length}
          </span>
        </div>
      </div>

      {/* Repair Orders Table */}
      <div className="table-container">
        <div className="table-wrapper-fixed">
          <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
            <thead className="table-header-fixed">
              <tr>
                <th style={{ textAlign: 'left', width: '80px', minWidth: '80px' }}>{t('qrCode', 'QR Code')}</th>
                <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{t('clientName', 'اسم العميل')}</th>
                <th style={{ textAlign: 'left', width: '140px', minWidth: '140px' }}>{t('deviceName', 'اسم الجهاز')}</th>
                <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{t('problem', 'المشكلة')}</th>
                <th style={{ textAlign: 'left', width: '100px', minWidth: '100px' }}>{t('status', 'الحالة')}</th>
                <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{t('partsPrice', 'Parts Price')}</th>
                <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{currentLanguage === 'ar' ? 'السعر الإجمالي' : currentLanguage === 'fr' ? 'Prix Total' : 'Total Price'}</th>
                <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{t('actions', 'الإجراءات')}</th>
              </tr>
            </thead>
          </table>
          <div className="table-body-scrollable" style={{ maxHeight: '240px', overflowY: 'auto' }}>
            <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
              <thead className="table-header-hidden">
                <tr>
                  <th style={{ textAlign: 'left', width: '80px', minWidth: '80px' }}>{t('qrCode', 'QR Code')}</th>
                  <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{t('clientName', 'اسم العميل')}</th>
                  <th style={{ textAlign: 'left', width: '140px', minWidth: '140px' }}>{t('deviceName', 'اسم الجهاز')}</th>
                  <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{t('problem', 'المشكلة')}</th>
                  <th style={{ textAlign: 'left', width: '100px', minWidth: '100px' }}>{t('status', 'الحالة')}</th>
                  <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{t('partsPrice', 'Parts Price')}</th>
                  <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{currentLanguage === 'ar' ? 'السعر الإجمالي' : currentLanguage === 'fr' ? 'Prix Total' : 'Total Price'}</th>
                  <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{t('actions', 'الإجراءات')}</th>
                </tr>
              </thead>
              <tbody>
                {filteredRepairs.length === 0 ? (
                  <tr>
                    <td colSpan="8" style={{ textAlign: 'center', padding: '20px' }}>
                      {repairFilter.trim() || statusFilter !== 'all'
                        ? t('noRepairsMatchingFilter', 'لا توجد إصلاحات مطابقة للفلتر')
                        : t('noRepairsFound', 'لا توجد إصلاحات')
                      }
                    </td>
                  </tr>
                ) : (
                  filteredRepairs.map((repair) => (
                    <tr key={repair.id}>
                      <td style={{ textAlign: 'left', width: '80px', minWidth: '80px' }}>
                        <div className="qr-code-cell">
                          {repairQRCodes[repair.id] ? (
                            <img
                              src={repairQRCodes[repair.id]}
                              alt="QR Code"
                              className="table-qr-code"
                              style={{ width: '40px', height: '40px', cursor: 'pointer' }}
                              onClick={() => showQRCodeModal(repair)}
                              title={t('clickToEnlarge', 'انقر للتكبير')}
                            />
                          ) : (
                            <button
                              className="btn btn-info btn-xs"
                              onClick={() => generateQRCodeForRepair(repair)}
                              title={t('generateQRCode', 'إنشاء رمز QR')}
                            >
                              📱
                            </button>
                          )}
                        </div>
                      </td>
                      <td style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{repair.clientName}</td>
                      <td style={{ textAlign: 'left', width: '140px', minWidth: '140px' }}>{repair.deviceName}</td>
                      <td style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{formatProblemType(repair.problemType)}</td>
                      <td style={{ textAlign: 'left', width: '100px', minWidth: '100px' }}>
                        <span className={`status-badge status-${repair.status?.replace(/\s+/g, '-')}`}>
                          {translateRepairStatus(repair.status)}
                        </span>
                      </td>
                      <td style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{formatPrice(repair.partsPrice || 0)}</td>
                      <td style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>
                        {repair.isFailedRepair || repair.status === 'enAttenteClient'
                          ? formatPrice(repair.verificationPrice || 0)
                          : formatPrice(repair.repairPrice || 0)
                        }
                      </td>
                  <td style={{ textAlign: 'left', width: '150px', minWidth: '150px', padding: '1rem' }}>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '0.3rem',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}>
                      {/* First row: View and Edit buttons */}
                      <div style={{
                        display: 'flex',
                        gap: '0.3rem',
                        justifyContent: 'center'
                      }}>
                        <button
                          onClick={() => {
                            setSelectedRepair(repair);
                            setShowRepairDetailsModal(true);
                          }}
                          title={t('viewRepairDetails', 'عرض تفاصيل الإصلاح')}
                          style={{
                            width: '32px',
                            height: '32px',
                            border: '1px solid #007bff',
                            background: '#007bff',
                            color: 'white',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.9rem',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.background = '#0056b3';
                            e.target.style.transform = 'translateY(-1px)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.background = '#007bff';
                            e.target.style.transform = 'translateY(0)';
                          }}
                        >
                          👁️
                        </button>
                        {(currentUser.role === 'مدير' || currentUser.role === 'admin') && (
                          <button
                            onClick={() => editRepairOrder(repair)}
                            title={t('editRepair', 'تعديل الإصلاح')}
                            style={{
                              width: '32px',
                              height: '32px',
                              border: '1px solid #28a745',
                              background: '#28a745',
                              color: 'white',
                              borderRadius: '6px',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '0.9rem',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.background = '#1e7e34';
                              e.target.style.transform = 'translateY(-1px)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = '#28a745';
                              e.target.style.transform = 'translateY(0)';
                            }}
                          >
                            ✏️
                          </button>
                        )}
                      </div>

                      {/* Second row: Print buttons */}
                      <div style={{
                        display: 'flex',
                        gap: '0.3rem',
                        justifyContent: 'center'
                      }}>
                        <button
                          onClick={async () => {
                            try {
                              await RepairThermalPrinter.openPasteTicketPrintWindow(repair, {
                                language: currentLanguage,
                                showToast: showToast
                              });
                              showToast(`🎫 ${t('printingPasteTicket', 'طباعة تذكرة اللصق')} ${repair.id}`, 'success', 2000);
                            } catch (error) {
                              console.error('Error printing paste ticket:', error);
                              showToast('❌ خطأ في طباعة تذكرة اللصق', 'error', 3000);
                            }
                          }}
                          title={t('printPasteTicket', 'طباعة تذكرة اللصق')}
                          style={{
                            width: '32px',
                            height: '32px',
                            border: '1px solid #ffc107',
                            background: '#ffc107',
                            color: '#212529',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.9rem',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.background = '#e0a800';
                            e.target.style.transform = 'translateY(-1px)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.background = '#ffc107';
                            e.target.style.transform = 'translateY(0)';
                          }}
                        >
                          🎫
                        </button>
                        <button
                          onClick={async () => {
                            try {
                              await RepairThermalPrinter.openPrintWindow(repair, {
                                language: currentLanguage,
                                showToast: showToast
                              });
                              showToast(`🖨️ ${t('printingRepairTicket', 'طباعة تذكرة الإصلاح')} ${repair.id}`, 'success', 2000);
                            } catch (error) {
                              console.error('Error printing repair ticket:', error);
                              showToast('❌ خطأ في طباعة تذكرة الإصلاح', 'error', 3000);
                            }
                          }}
                          title={t('printBonPour', 'طباعة بون بور')}
                          style={{
                            width: '32px',
                            height: '32px',
                            border: '1px solid #17a2b8',
                            background: '#17a2b8',
                            color: 'white',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.9rem',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.background = '#138496';
                            e.target.style.transform = 'translateY(-1px)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.background = '#17a2b8';
                            e.target.style.transform = 'translateY(0)';
                          }}
                        >
                          🖨️
                        </button>
                      </div>

                      {/* Third row: Delete button (admin only) */}
                      {(currentUser.role === 'مدير' || currentUser.role === 'admin') && (
                        <button
                          onClick={() => deleteRepair(repair.id)}
                          title={t('deleteRepair', 'حذف الإصلاح')}
                          style={{
                            width: '32px',
                            height: '32px',
                            border: '1px solid #dc3545',
                            background: '#dc3545',
                            color: 'white',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '0.9rem',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.background = '#c82333';
                            e.target.style.transform = 'translateY(-1px)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.background = '#dc3545';
                            e.target.style.transform = 'translateY(0)';
                          }}
                        >
                          🗑️
                        </button>
                      )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Supplier Parts Reparation Table */}
      <div className="supplier-parts-section" style={{ marginTop: '3rem' }}>
        <div className={`section-header suppliers-header-with-button ${currentLanguage !== 'ar' ? 'header-ltr' : 'header-rtl'}`} style={{ background: 'linear-gradient(135deg, #29B6F6, #1E88E5)' }}>
          <div className="header-content">
            <h2 style={{
              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
              direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
              textAlign: currentLanguage === 'ar' ? 'right' : 'left'
            }}>
              🔧 {currentLanguage === 'ar' ? 'إدارة الموردين والمعاملات' :
                  currentLanguage === 'fr' ? 'Gestion des fournisseurs de pièces et transactions' :
                  'Suppliers and Transactions Management'}
            </h2>

          </div>
          <div className="header-actions">
            <button
              className="btn-modern btn-success"
              onClick={() => setShowAddSupplierModal(true)}
              style={{
                fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
              }}
            >
              <span className="btn-icon">➕</span>
              <span className="btn-text">
                {currentLanguage === 'ar' ? 'إضافة مورد جديد' :
                 currentLanguage === 'fr' ? 'Ajouter Nouveau Fournisseur' :
                 'Add New Supplier'}
              </span>
            </button>
          </div>
        </div>

        <div className="table-container">
          <div className="table-wrapper-fixed">
            <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
              <thead className="table-header-fixed" style={{ background: 'linear-gradient(135deg, #29B6F6, #1E88E5)' }}>
                <tr>
                  <th style={{ textAlign: 'left', width: '200px', minWidth: '200px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{currentLanguage === 'ar' ? 'المورد' : currentLanguage === 'fr' ? 'Fournisseur' : 'Supplier'}</th>
                  <th style={{ textAlign: 'left', width: '150px', minWidth: '150px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{currentLanguage === 'ar' ? 'إجمالي المعاملات' : currentLanguage === 'fr' ? 'Total Transactions' : 'Total Transactions'}</th>
                  <th style={{ textAlign: 'left', width: '150px', minWidth: '150px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{currentLanguage === 'ar' ? 'المبلغ الإجمالي' : currentLanguage === 'fr' ? 'Montant Total' : 'Total Amount'}</th>
                  <th style={{ textAlign: 'left', width: '120px', minWidth: '120px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{currentLanguage === 'ar' ? 'المدفوع' : currentLanguage === 'fr' ? 'Payé' : 'Paid'}</th>
                  <th style={{ textAlign: 'left', width: '120px', minWidth: '120px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{currentLanguage === 'ar' ? 'المعلق' : currentLanguage === 'fr' ? 'En Attente' : 'Pending'}</th>
                  <th style={{ textAlign: 'left', width: '200px', minWidth: '200px', background: 'linear-gradient(135deg, #29B6F6, #1E88E5)', color: 'white' }}>{t('actions', 'الإجراءات')}</th>
                </tr>
              </thead>
            </table>
            <div className="table-body-scrollable" style={{ maxHeight: '240px', overflowY: 'auto' }}>
              <table className={`data-table ${currentLanguage !== 'ar' ? 'table-ltr' : ''}`}>
                <thead className="table-header-hidden">
                  <tr>
                    <th style={{ textAlign: 'left', width: '200px', minWidth: '200px' }}>{currentLanguage === 'ar' ? 'المورد' : currentLanguage === 'fr' ? 'Fournisseur' : 'Supplier'}</th>
                    <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{currentLanguage === 'ar' ? 'إجمالي المعاملات' : currentLanguage === 'fr' ? 'Total Transactions' : 'Total Transactions'}</th>
                    <th style={{ textAlign: 'left', width: '150px', minWidth: '150px' }}>{currentLanguage === 'ar' ? 'المبلغ الإجمالي' : currentLanguage === 'fr' ? 'Montant Total' : 'Total Amount'}</th>
                    <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{currentLanguage === 'ar' ? 'المدفوع' : currentLanguage === 'fr' ? 'Payé' : 'Paid'}</th>
                    <th style={{ textAlign: 'left', width: '120px', minWidth: '120px' }}>{currentLanguage === 'ar' ? 'المعلق' : currentLanguage === 'fr' ? 'En Attente' : 'Pending'}</th>
                    <th style={{ textAlign: 'left', width: '200px', minWidth: '200px' }}>{t('actions', 'الإجراءات')}</th>
                  </tr>
                </thead>
                <tbody>
                  {(() => {
                    // Group transactions by supplier
                    const supplierGroups = suppliersPartsReparationData.reduce((groups, transaction) => {
                      const supplier = transaction.supplierName;
                      if (!groups[supplier]) {
                        groups[supplier] = [];
                      }
                      groups[supplier].push(transaction);
                      return groups;
                    }, {});

                    return Object.keys(supplierGroups).length === 0 ? (
                      <tr>
                        <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                          {currentLanguage === 'ar' ? 'لا توجد معاملات موردين' :
                           currentLanguage === 'fr' ? 'Aucune transaction fournisseur' :
                           'No supplier transactions'}
                        </td>
                      </tr>
                    ) : (
                      Object.keys(supplierGroups).map(supplierName => {
                        const transactions = supplierGroups[supplierName];

                        // Calculate total credit (positive amounts only)
                        const totalCredit = transactions
                          .filter(t => (t.status === 'credit' || !t.status) && parseFloat(t.price) > 0)
                          .reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0);

                        // Calculate total payments (negative amounts and partial payments)
                        const totalPayments = transactions
                          .filter(t => t.status === 'payment' || parseFloat(t.price) < 0)
                          .reduce((sum, t) => sum + Math.abs(parseFloat(t.price) || 0), 0) +
                          transactions
                          .filter(t => (t.status === 'credit' || !t.status))
                          .reduce((sum, t) => sum + (parseFloat(t.partialPayment) || 0), 0);

                        const pendingAmount = totalCredit - totalPayments;

                    return (
                      <tr key={supplierName} style={{
                        borderBottom: '1px solid #e9ecef',
                        transition: 'all 0.2s ease',
                        '&:hover': { backgroundColor: '#f8f9fa' }
                      }}>
                        <td style={{
                          fontWeight: '600',
                          fontSize: '1.1rem',
                          textAlign: 'left',
                          width: '200px',
                          minWidth: '200px',
                          padding: '12px 8px',
                          color: '#2c3e50'
                        }}>{supplierName}</td>
                        <td style={{
                          textAlign: 'center',
                          width: '150px',
                          minWidth: '150px',
                          padding: '12px 8px',
                          fontSize: '1rem'
                        }}>
                          <span style={{
                            background: 'linear-gradient(135deg, #6c757d, #495057)',
                            color: 'white',
                            padding: '4px 12px',
                            borderRadius: '20px',
                            fontSize: '0.9rem',
                            fontWeight: '500'
                          }}>
                            {transactions.length}
                          </span>
                        </td>
                        <td style={{
                          textAlign: 'center',
                          width: '150px',
                          minWidth: '150px',
                          padding: '12px 8px',
                          fontSize: '1rem',
                          fontWeight: '600'
                        }}>{formatPrice(totalCredit)}</td>
                        <td style={{
                          textAlign: 'center',
                          width: '120px',
                          minWidth: '120px',
                          padding: '12px 8px'
                        }}>
                          <span style={{
                            background: 'linear-gradient(135deg, #28a745, #20c997)',
                            color: 'white',
                            padding: '4px 12px',
                            borderRadius: '20px',
                            fontSize: '0.9rem',
                            fontWeight: '500'
                          }}>
                            {formatPrice(totalPayments)}
                          </span>
                        </td>
                        <td style={{
                          textAlign: 'center',
                          width: '120px',
                          minWidth: '120px',
                          padding: '12px 8px'
                        }}>
                          <span style={{
                            background: pendingAmount > 0 ?
                              'linear-gradient(135deg, #dc3545, #c82333)' :
                              'linear-gradient(135deg, #28a745, #20c997)',
                            color: 'white',
                            padding: '4px 12px',
                            borderRadius: '20px',
                            fontSize: '0.9rem',
                            fontWeight: '500'
                          }}>
                            {formatPrice(pendingAmount)}
                          </span>
                        </td>
                        <td style={{ textAlign: 'left', width: '200px', minWidth: '200px' }}>
                          <div className="action-buttons-group">
                            <button
                              className="btn btn-info btn-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedSupplierparts(supplierName);
                                setShowSupplierTransactionparts(true);
                              }}
                              title={currentLanguage === 'ar' ? 'عرض المعاملات' : 'View Transactions'}
                            >
                              👁️
                            </button>
                            <button
                              className="btn btn-success btn-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedSupplierparts(supplierName);
                                setShowSupplierpartsPayment(true);
                              }}
                              title={currentLanguage === 'ar' ? 'دفع للمورد' : 'Pay Supplier'}
                            >
                              💰
                            </button>
                            <button
                              className="btn btn-warning btn-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedSupplierparts(supplierName);
                                setShowSupplierpartsEdit(true);
                              }}
                              title={currentLanguage === 'ar' ? 'تعديل المورد' : 'Edit Supplier'}
                            >
                              ✏️
                            </button>
                            <button
                              className="btn btn-secondary btn-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                printSupplierTransactions(supplierName);
                              }}
                              title={currentLanguage === 'ar' ? 'طباعة التقرير' : 'Print Report'}
                            >
                              🖨️
                            </button>
                            <button
                              className="btn btn-danger btn-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedSupplierparts(supplierName);
                                setShowSupplierpartsDelete(true);
                              }}
                              title={currentLanguage === 'ar' ? 'حذف المورد' : 'Delete Supplier'}
                            >
                              🗑️
                            </button>
                          </div>
                        </td>
                        </tr>
                      );
                    })
                  );
                })()}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Modern New Repair Order Modal - Complete Implementation */}
      {showNewRepairModal && (
        <div className="modal-overlay modern-overlay" onClick={closeNewRepairModal}>
          <div className={`modal-content nouveau-bon-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header nouveau-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">📝</div>
                <div className="header-text">
                  <h2>
                    {selectedRepair ?
                      (currentLanguage === 'ar' ? 'تعديل أمر الإصلاح' :
                       currentLanguage === 'fr' ? 'Modifier l\'ordre de réparation' :
                       'Edit Repair Order') :
                      (currentLanguage === 'ar' ? 'إنشاء أمر إصلاح جديد' :
                       currentLanguage === 'fr' ? 'Nouvel ordre de réparation' :
                       'Create New Repair Order')
                    }
                  </h2>
                  <p>{currentLanguage === 'ar' ? 'معلومات العميل والجهاز' :
                      currentLanguage === 'fr' ? 'Informations Client et Appareil' :
                      'Client and Device Information'}</p>
                </div>
              </div>
              <button className="modal-close-btn" onClick={closeNewRepairModal}>✕</button>
            </div>

            <div className="nouveau-form-container">
              <form onSubmit={async (e) => { e.preventDefault(); await createRepairOrder(); }} className="nouveau-form">
                {/* Client & Device Section - Side by Side */}
                <div className="form-section">
                  <div className="section-title">
                    <span className="section-icon">👤</span>
                    <h3>{currentLanguage === 'ar' ? 'معلومات العميل والجهاز' : currentLanguage === 'fr' ? 'Informations Client et Appareil' : 'Client and Device Information'}</h3>
                  </div>
                  <div className="form-grid">
                    <div className="form-field">
                      <label className="modern-label">{t('clientFullName', 'Nom Complet du Client')}</label>
                      <input
                        type="text"
                        className="modern-input"
                        value={newRepairForm.clientName}
                        onChange={(e) => setNewRepairForm({...newRepairForm, clientName: e.target.value})}
                        onFocus={(e) => e.target.select()}
                        required
                        placeholder={t('enterClientName', 'Entrez le nom du client')}
                      />
                    </div>
                    <div className="form-field">
                      <label className="modern-label">{t('deviceName', 'Nom de l\'Appareil')}</label>
                      <input
                        type="text"
                        className="modern-input"
                        value={newRepairForm.deviceName}
                        onChange={(e) => setNewRepairForm({...newRepairForm, deviceName: e.target.value})}
                        onFocus={(e) => e.target.select()}
                        required
                        placeholder={t('enterDeviceName', 'Entrez le nom de l\'appareil')}
                      />
                    </div>
                  </div>
                  <div className="form-grid">
                    <div className="form-field">
                      <label className="modern-label">{currentLanguage === 'ar' ? 'رقم الهاتف' : currentLanguage === 'fr' ? 'Numéro de Téléphone' : 'Phone Number'}</label>
                      <input
                        type="tel"
                        className="modern-input"
                        value={newRepairForm.clientPhone}
                        onChange={(e) => {
                          // Only allow integers (digits only)
                          const value = e.target.value.replace(/[^0-9]/g, '');
                          setNewRepairForm({...newRepairForm, clientPhone: value});
                        }}
                        onFocus={(e) => e.target.select()}
                        placeholder={currentLanguage === 'ar' ? 'أدخل رقم الهاتف' : currentLanguage === 'fr' ? 'Entrez le numéro de téléphone' : 'Enter phone number'}
                      />
                    </div>
                  </div>
                </div>

                {/* Problem & Type Section */}
                <div className="form-section">
                  <div className="section-title">
                    <span className="section-icon">🔧</span>
                    <h3>{t('problemTypeInfo', 'Type d\'Appareil & Problème')}</h3>
                  </div>
                  <div className="form-grid">
                    <div className="form-field">
                      <label className="modern-label">{t('deviceType', 'Type d\'Appareil')}</label>
                      <select
                        className="modern-select"
                        value={newRepairForm.deviceType}
                        onChange={(e) => setNewRepairForm({...newRepairForm, deviceType: e.target.value})}
                        required
                      >
                        <option value="">{t('selectDeviceType', 'Select device type')}</option>
                        <option value="smartphone">{t('smartphone', 'Smartphone')}</option>
                        <option value="tablet">{t('tablet', 'Tablet')}</option>
                        <option value="pc">{t('pc', 'Computer')}</option>
                        <option value="console">{t('console', 'Game console')}</option>
                      </select>
                    </div>
                    <div className="form-field">
                      <label className="modern-label">{t('problemDescription', 'Problem Description')}</label>
                      <select
                        className="modern-select"
                        value={newRepairForm.problemType}
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === 'addNewProblem') {
                            setShowProblemManagerModal(true);
                          } else if (value.startsWith('custom_')) {
                            const customProblem = customProblems.find(p => p.id === value.replace('custom_', ''));
                            if (customProblem) {
                              setNewRepairForm({
                                ...newRepairForm,
                                problemType: value,
                                problemDescription: customProblem.description
                              });
                            }
                          } else {
                            setNewRepairForm({
                              ...newRepairForm,
                              problemType: value,
                              problemDescription: t(value, value)
                            });
                          }
                        }}
                        required
                      >
                        <option value="">{t('selectProblem', 'Sélectionner le problème')}</option>
                        <option value="lcd">{t('lcd', 'Écran LCD')}</option>
                        <option value="lcdWithFrame">{t('lcdWithFrame', 'LCD avec cadre')}</option>
                        <option value="chargingPort">{t('chargingPort', 'Port de charge')}</option>
                        <option value="glass">{t('glass', 'Verre')}</option>
                        <option value="backGlass">{t('backGlass', 'Verre arrière')}</option>
                        <option value="battery">{t('battery', 'Batterie')}</option>
                        <option value="frontCamera">{t('frontCamera', 'Caméra avant')}</option>
                        <option value="rearCamera">{t('rearCamera', 'Caméra arrière')}</option>
                        <option value="waterDamage">{t('waterDamage', 'Dégât des eaux')}</option>
                        {customProblems.length > 0 && (
                          <optgroup label={t('customProblems', 'Problèmes personnalisés')}>
                            {customProblems.map(problem => (
                              <option key={problem.id} value={`custom_${problem.id}`}>
                                {problem.name}
                              </option>
                            ))}
                          </optgroup>
                        )}
                        <option value="addNewProblem">{t('addNewProblem', '+ Gérer les problèmes')}</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Pricing & Payment Section */}
                <div className="form-section">
                  <div className="section-title">
                    <span className="section-icon">💰</span>
                    <h3>{t('pricingPaymentInfo', 'Pricing & Payment')}</h3>
                  </div>
                  <div className="form-grid">
                    <div className="form-field">
                      <label className="modern-label">{t('repairPrice', 'Repair Price')}</label>
                      <div className="input-with-currency">
                        <input
                          type="number"
                          className="modern-input price-input"
                          value={newRepairForm.repairPrice}
                          onChange={(e) => setNewRepairForm({...newRepairForm, repairPrice: e.target.value})}
                          onFocus={(e) => e.target.select()}
                          required
                          min="0"
                          step="0.01"
                          placeholder="2400"
                        />
                        <span className="currency">DZD</span>
                      </div>
                    </div>
                    <div className="form-field">
                      <label className="modern-label">{t('partialPayment', 'Paiement Partiel')}</label>
                      <div className="input-with-currency">
                        <input
                          type="number"
                          className="modern-input price-input"
                          value={newRepairForm.partialPayment}
                          onChange={(e) => setNewRepairForm({...newRepairForm, partialPayment: e.target.value})}
                          onFocus={(e) => e.target.select()}
                          min="0"
                          step="0.01"
                          placeholder="2400"
                        />
                        <span className="currency">DZD</span>
                      </div>
                    </div>
                  </div>
                  <div className="payment-status-section">
                    <label className="modern-label">{t('paymentStatus', 'Statut de Paiement')}</label>
                    <div className="modern-radio-group">
                      <label className="modern-radio">
                        <input
                          type="radio"
                          name="paymentStatus"
                          value="paid"
                          checked={newRepairForm.paymentStatus === 'paid'}
                          onChange={(e) => setNewRepairForm({...newRepairForm, paymentStatus: e.target.value})}
                        />
                        <span className="radio-checkmark"></span>
                        <span className="radio-text">{t('paid', 'Payé')}</span>
                      </label>
                      <label className="modern-radio">
                        <input
                          type="radio"
                          name="paymentStatus"
                          value="partiallyPaid"
                          checked={newRepairForm.paymentStatus === 'partiallyPaid'}
                          onChange={(e) => setNewRepairForm({...newRepairForm, paymentStatus: e.target.value})}
                        />
                        <span className="radio-checkmark"></span>
                        <span className="radio-text">{t('partiallyPaid', 'Partiellement Payé')}</span>
                      </label>
                      <label className="modern-radio">
                        <input
                          type="radio"
                          name="paymentStatus"
                          value="unpaid"
                          checked={newRepairForm.paymentStatus === 'unpaid'}
                          onChange={(e) => setNewRepairForm({...newRepairForm, paymentStatus: e.target.value})}
                        />
                        <span className="radio-checkmark"></span>
                        <span className="radio-text">{t('unpaid', 'Non Payé')}</span>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Date & Details Section */}
                <div className="form-section">
                  <div className="section-title">
                    <span className="section-icon">📅</span>
                    <h3>{t('dateDetailsInfo', 'Date & Details')}</h3>
                  </div>
                  <div className="form-grid">
                    <div className="form-field">
                      <label className="modern-label">{t('depositDate', 'Deposit Date')}</label>
                      <input
                        type="date"
                        className="modern-input readonly-input"
                        value={newRepairForm.depositDate}
                        readOnly
                      />
                    </div>
                    <div className="form-field">
                      <label className="modern-label">{t('depositTime', 'Deposit Time')}</label>
                      <input
                        type="time"
                        className="modern-input readonly-input"
                        value={newRepairForm.depositTime}
                        readOnly
                      />
                    </div>
                  </div>
                  {/* Hidden barcode and remarks fields as per 3pagenew.md requirements */}
                  <div className="form-grid" style={{display: 'none'}}>
                    <div className="form-field">
                      <label className="modern-label">{t('repairBarcode', 'Code-barres')}</label>
                      <input
                        type="text"
                        className="modern-input readonly-input barcode-input"
                        value={newRepairForm.repairBarcode || generateRepairBarcode()}
                        readOnly
                        placeholder={t('autoGenerated', 'Généré automatiquement')}
                      />
                    </div>
                    <div className="form-field">
                      <label className="modern-label">{t('remarks', 'Remarques')}</label>
                      <textarea
                        className="modern-textarea"
                        value={newRepairForm.remarks}
                        onChange={(e) => setNewRepairForm({...newRepairForm, remarks: e.target.value})}
                        placeholder={t('enterRemarks', 'Entrez des remarques supplémentaires')}
                        rows="3"
                      />
                    </div>
                  </div>

                </div>

                {/* Clean Form Actions at bottom */}
                <div className="nouveau-form-actions" style={{
                  display: 'flex',
                  gap: '1rem',
                  justifyContent: 'center',
                  padding: '2rem 0',
                  borderTop: '1px solid rgba(0, 0, 0, 0.1)',
                  marginTop: '2rem'
                }}>
                  <button type="submit" className="btn-modern btn-primary" style={{
                    minWidth: '150px',
                    padding: '12px 24px',
                    fontSize: '1rem',
                    fontWeight: '600'
                  }}>
                    <span className="btn-icon">💾</span>
                    <span className="btn-text">
                      {selectedRepair ? t('updateRepair', 'Mettre à jour') :
                       (currentLanguage === 'ar' ? 'حفظ' :
                        currentLanguage === 'fr' ? 'Enregistrer' :
                        'Save')}
                    </span>
                  </button>
                  <button type="button" className="btn-modern btn-secondary" onClick={closeNewRepairModal} style={{
                    minWidth: '150px',
                    padding: '12px 24px',
                    fontSize: '1rem',
                    fontWeight: '600'
                  }}>
                    <span className="btn-icon">❌</span>
                    <span className="btn-text">{t('cancel', 'Annuler')}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modern Réparation Terminée Modal - Rebuilt from App_old.jsx */}
      {showRepairCompletedModal && (
        <div className="modal-overlay modern-overlay" onClick={closeRepairCompletedModal}>
          <div className={`modal-content reparation-terminee-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header reparation-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">🔧</div>
                <div className="header-text">
                  <h2>
                    {currentLanguage === 'ar' ? 'اختيار الإصلاح للإنهاء' :
                     currentLanguage === 'fr' ? 'Sélectionner la réparation à finaliser' :
                     'Select Repair to Complete'}
                  </h2>
                  <p>
                    {currentLanguage === 'ar' ? 'مسح رمز QR أو اختيار يدوي' :
                     currentLanguage === 'fr' ? 'Scanner le QR code ou sélectionner manuellement' :
                     'Scan QR code or select manually'}
                  </p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={closeRepairCompletedModal}>×</button>
            </div>

            <div className="reparation-container">
              {/* Step 1: Select Repair */}
              {!selectedRepairForCompletion && (
                <div className="step-section">
                  {/* Removed duplicate text as per 3pagenew.md requirements */}

                  {/* Large Row Layout: Scanner/Search and Table Side by Side */}
                  <div style={{display: 'flex', gap: '2rem', alignItems: 'flex-start', width: '100%', minHeight: '500px'}}>
                    {/* Left Column: Scanner and Manual Search */}
                    <div style={{flex: '1', minWidth: '300px'}}>
                      {/* Barcode Scanner Section */}
                      <div className="pickup-barcode-section">
                        <div className="scanner-header">
                          <div className="scanner-icon-large">📷</div>
                          <div className="scanner-info">
                            <h4 style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}>{currentLanguage === 'ar' ? 'مسح رمز QR للمعلومات التلقائية للعميل' :
                                currentLanguage === 'fr' ? 'Scanner le QR pour les informations automatiques du client' :
                                'Scan QR for automatic client information'}</h4>
                            <p style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}>{t('quickestMethod', 'Quickest method')}</p>
                          </div>
                        </div>
                        <div className="barcode-input-container">
                          <input
                            type="text"
                            className="barcode-scanner-input"
                            placeholder={currentLanguage === 'ar' ? 'مسح رمز QR للمعلومات التلقائية للعميل' :
                                        currentLanguage === 'fr' ? 'Scanner le QR pour les informations automatiques du client' :
                                        'Scan QR for automatic client information'}
                            style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}
                            autoFocus
                            onFocus={() => {
                              if (window.barcodeShortcutManager) {
                                window.barcodeShortcutManager.isBarcodeActive = true;
                                window.barcodeShortcutManager.setShortcutsEnabled(false);
                              }
                            }}
                            onBlur={() => {
                              setTimeout(() => {
                                if (window.barcodeShortcutManager && !window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)) {
                                  window.barcodeShortcutManager.isBarcodeActive = false;
                                  window.barcodeShortcutManager.setShortcutsEnabled(true);
                                }
                              }, 100);
                            }}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                const barcode = e.target.value.trim();
                                const foundRepair = repairsData.find(repair =>
                                  repair.repairBarcode === barcode && repair.status === 'inProgress'
                                );
                                if (foundRepair) {
                                  setSelectedRepairForCompletion(foundRepair);
                                  showToast(t('repairFound', 'Repair found'), 'success');
                                  e.target.value = '';
                                } else {
                                  showToast(t('repairNotFound', 'Repair not found'), 'error');
                                }
                              }
                            }}
                          />
                          <span className="barcode-scanner-icon">📷</span>
                        </div>
                      </div>

                      {/* Manual Search Section */}
                      <div className="pickup-filter-section" style={{marginTop: '1.5rem'}}>
                        <div className="filter-header">
                          <h4>{t('orSearchManually', 'Ou Rechercher Manuellement')}</h4>
                        </div>
                        <div className="search-filters">
                          <input
                            type="text"
                            className="filter-input"
                            placeholder={t('searchByNamePhoneDate', 'Search by name, phone, or date...')}
                            value={repairFilter || ''}
                            onChange={(e) => setRepairFilter(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Right Column: Repairs Table */}
                    <div style={{flex: '2', minWidth: '600px'}}>
                      <div className="repairs-table-container reparation-table-fixed">
                        {repairsData.filter(repair =>
                          repair.status === 'inProgress' &&
                          (!repairFilter ||
                            repair.clientName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            repair.deviceName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            repair.problemDescription.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            repair.repairBarcode?.toLowerCase().includes(repairFilter.toLowerCase())
                          )
                        ).length === 0 ? (
                          <div className="empty-state">
                            <div className="empty-icon">📋</div>
                            <h4>{t('noInProgressRepairs', 'No repairs in progress currently')}</h4>
                            <p>{t('createNewRepairFirst', 'Create a new repair first or check repair statuses')}</p>
                          </div>
                        ) : (
                          <div className="table-wrapper-fixed">
                            <table className="repairs-table modern-table reparation-fixed-table">
                              <thead className="table-header-fixed">
                                <tr style={{height: '50px', backgroundColor: '#498C8A', color: 'white'}}>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#498C8A', color: 'white'}}>{t('clientName', 'Client Name')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#498C8A', color: 'white'}}>{t('deviceNameHeader', 'Device Name')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#498C8A', color: 'white'}}>{t('problem', 'Problem')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#498C8A', color: 'white'}}>{t('repairPrice', 'Repair Price')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#498C8A', color: 'white'}}>{t('depositDate', 'Deposit Date')}</th>
                                </tr>
                              </thead>
                            </table>
                            <div className="table-body-scrollable repairs-table-container">
                              <table className="repairs-table modern-table reparation-fixed-table">
                                <thead className="table-header-hidden">
                                  <tr style={{height: '50px'}}>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('clientName', 'Client Name')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('deviceNameHeader', 'Device Name')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('problem', 'Problem')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('repairPrice', 'Repair Price')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('depositDate', 'Deposit Date')}</th>
                                  </tr>
                                </thead>
                                <tbody>
                              {repairsData.filter(repair =>
                                repair.status === 'inProgress' &&
                                (!repairFilter ||
                                  repair.clientName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                  repair.deviceName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                  repair.problemDescription.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                  repair.repairBarcode?.toLowerCase().includes(repairFilter.toLowerCase())
                                )
                              ).map(repair => (
                                <tr
                                  key={repair.id}
                                  className="repair-row clickable-row"
                                  style={{height: '50px'}}
                                  onClick={() => setSelectedRepairForCompletion(repair)}
                                >
                                  <td className="client-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                    <div className="client-info">
                                      <span className="client-name">{repair.clientName}</span>
                                      <span className="client-phone">{repair.clientPhone}</span>
                                    </div>
                                  </td>
                                  <td className="device-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                    <span className="device-name">{repair.deviceName}</span>
                                    <span className="device-type">{repair.deviceType}</span>
                                  </td>
                                  <td className="problem-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                    <span className="problem-desc">{repair.problemDescription}</span>
                                  </td>
                                  <td className="price-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                    <span className="repair-price">{formatPrice(repair.repairPrice || 0)}</span>
                                  </td>
                                  <td className="date-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                    <span className="deposit-date">{new Date(repair.createdAt || repair.depositDate).toLocaleDateString()}</span>
                                  </td>
                                </tr>
                              ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Choose Outcome */}
              {selectedRepairForCompletion && (
                <div className="step-section">
                  <div className="step-header">
                    <div className="step-number">2</div>
                    <div className="step-info">
                      <h3>{t('chooseRepairOutcome', 'Choose Repair Outcome')}</h3>
                      <p>{t('selectSuccessOrFailure', 'Select whether the repair succeeded or failed')}</p>
                    </div>
                    <button
                      className="btn-back"
                      onClick={() => setSelectedRepairForCompletion(null)}
                    >
                      ← {t('back', 'Retour')}
                    </button>
                  </div>

                  <div className="selected-repair-summary">
                    <div className="summary-icon">📱</div>
                    <div className="summary-info">
                      <h4>{selectedRepairForCompletion.clientName}</h4>
                      <p>{selectedRepairForCompletion.deviceName}</p>
                      <span className="original-price">{formatPrice(selectedRepairForCompletion.repairPrice || 0)}</span>
                    </div>
                  </div>

                  <div className="outcome-options">
                    {/* Success Option */}
                    <div className="outcome-card success-card">
                      <div className="outcome-header">
                        <div className="outcome-icon success-icon">✅</div>
                        <div className="outcome-info">
                          <h4>{t('repairSuccess', 'Repair Success')}</h4>
                          <p>{t('repairSuccessDescription', 'Repair completed successfully - will be marked as \'Waiting for Client\'')}</p>
                        </div>
                      </div>

                      <div className="outcome-form">
                        <div className="form-grid">
                          <div className="form-field">
                            <label className="modern-label">{t('partsChangedPrice', 'Prix des Pièces Changées')}</label>
                            <div className="input-with-currency">
                              <input
                                type="number"
                                className="modern-input price-input"
                                placeholder="2400"
                                min="0"
                                step="0.01"
                                value={repairCompletionForm.partsPrice}
                                onChange={(e) => setRepairCompletionForm(prev => ({
                                  ...prev,
                                  partsPrice: e.target.value
                                }))}
                              />
                              <span className="currency">DZD</span>
                            </div>
                          </div>
                          <div className="form-field">
                            <label className="modern-label">{t('supplierName', 'Nom du Fournisseur')}</label>
                            <select
                              className="modern-select"
                              value={repairCompletionForm.supplierName}
                              onChange={(e) => setRepairCompletionForm(prev => ({
                                ...prev,
                                supplierName: e.target.value
                              }))}
                            >
                              <option value="">{t('selectSupplier', 'Sélectionner un fournisseur')}</option>
                              {(() => {
                                // Get suppliers ONLY from repair parts data (not from other pages)
                                const repairSuppliers = [...new Set(
                                  suppliersPartsReparationData.map(item => item.supplierName)
                                )].filter(name => name && name.trim() !== '');

                                return repairSuppliers.map(supplierName => (
                                  <option key={supplierName} value={supplierName}>
                                    {supplierName}
                                  </option>
                                ));
                              })()}
                            </select>
                          </div>
                        </div>

                        <div className="form-field full-width">
                          <label className="modern-label">{t('remarks', 'Remarques')} ({t('optional', 'Optionnel')})</label>
                          <textarea
                            className="modern-textarea"
                            placeholder={t('enterRemarks', 'Entrez des remarques supplémentaires')}
                            rows="3"
                            value={repairCompletionForm.remarks}
                            onChange={(e) => setRepairCompletionForm(prev => ({
                              ...prev,
                              remarks: e.target.value
                            }))}
                          />
                        </div>

                        <div className="button-center-wrapper">
                          <button
                            className="btn-modern btn-success centered-action-btn"
                            onClick={() => {
                              if (selectedRepairForCompletion) {
                                markRepairCompleted(
                                  selectedRepairForCompletion.id,
                                  repairCompletionForm.partsPrice,
                                  repairCompletionForm.remarks,
                                  repairCompletionForm.supplierName
                                );
                              }
                            }}
                          >
                            <span className="btn-icon">✅</span>
                            <span className="btn-text">{t('markAsCompleted', 'Choose repair outcome to update status accordingly')}</span>
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Failure Option */}
                    <div className="outcome-card failure-card">
                      <div className="outcome-header">
                        <div className="outcome-icon failure-icon">❌</div>
                        <div className="outcome-info">
                          <h4>{t('repairNotSuccess', 'Repair Not Success')}</h4>
                          <p>{t('repairNotSuccessDescription', 'Repair not completed - will be marked as \'Not Success\'')}</p>
                        </div>
                      </div>

                      <div className="outcome-form">
                        <div className="form-field">
                          <label className="modern-label">{t('verificationPrice', 'Prix de Vérification')}</label>
                          <div className="input-with-currency">
                            <input
                              type="number"
                              className="modern-input price-input"
                              placeholder="2400"
                              min="0"
                              step="0.01"
                              value={repairCompletionForm.verificationPrice || ''}
                              onChange={(e) => setRepairCompletionForm(prev => ({
                                ...prev,
                                verificationPrice: e.target.value
                              }))}
                            />
                            <span className="currency">DZD</span>
                          </div>
                          <small className="field-note">{t('verificationPriceNote', 'Prix facturé pour le diagnostic/vérification')}</small>
                        </div>

                        <div className="form-field">
                          <label className="modern-label">{t('failureReason', 'Raison de l\'Échec')}</label>
                          <textarea
                            className="modern-textarea"
                            placeholder={t('explainFailureReason', 'Expliquez la raison de l\'échec')}
                            rows="4"
                            value={repairCompletionForm.failureRemarks}
                            onChange={(e) => setRepairCompletionForm(prev => ({
                              ...prev,
                              failureRemarks: e.target.value
                            }))}
                          />
                        </div>

                        <div className="button-center-wrapper">
                          <button
                            className="btn-modern btn-danger centered-action-btn"
                            onClick={() => {
                              if (selectedRepairForCompletion) {
                                markRepairFailed(
                                  selectedRepairForCompletion.id,
                                  repairCompletionForm.failureRemarks,
                                  repairCompletionForm.verificationPrice
                                );
                              }
                            }}
                          >
                            <span className="btn-icon">❌</span>
                            <span className="btn-text">{t('markAsNotSuccess', 'Mark as Not Success')}</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Modern Récupération Client Modal - Rebuilt from App_old.jsx */}
      {showClientPickupModal && (
        <div className="modal-overlay modern-overlay" onClick={closeClientPickupModal}>
          <div className={`modal-content recuperation-client-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header recuperation-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">📱</div>
                <div className="header-text">
                  <h2>
                    {currentLanguage === 'ar' ? 'العثور على الإصلاح للاستلام' :
                     currentLanguage === 'fr' ? 'Trouver la Réparation à Récupérer' :
                     'Find Repair to Recover'}
                  </h2>
                  <p>
                    {currentLanguage === 'ar' ? 'مسح رمز QR أو اختيار يدوي' :
                     currentLanguage === 'fr' ? 'Scanner le QR code ou sélectionner manuellement' :
                     'Scan QR code or select manually'}
                  </p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={closeClientPickupModal}>×</button>
            </div>

            <div className="recuperation-container">
              {/* Step 1: Find Repair */}
              {!selectedRepair && (
                <div className="step-section">
                  {/* Removed duplicate text as per 3pagenew.md requirements */}

                  {/* Large Row Layout: Scanner/Search and Table Side by Side */}
                  <div style={{display: 'flex', gap: '2rem', alignItems: 'flex-start', width: '100%', minHeight: '500px'}}>
                    {/* Left Column: Scanner and Manual Search */}
                    <div style={{flex: '1', minWidth: '300px'}}>
                      {/* Barcode Scanner Section - Auto Focus */}
                      <div className="pickup-barcode-section">
                        <div className="scanner-header">
                          <div className="scanner-icon-large">📷</div>
                          <div className="scanner-info">
                            <h4 style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}>{currentLanguage === 'ar' ? 'مسح رمز QR للمعلومات التلقائية للعميل' :
                                currentLanguage === 'fr' ? 'Scanner le QR pour les informations automatiques du client' :
                                'Scan QR for automatic client information'}</h4>
                            <p style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}>{t('quickestMethod', 'Quickest method')}</p>
                          </div>
                        </div>
                        <div className="barcode-input-container">
                          <input
                            type="text"
                            className="barcode-scanner-input"
                            placeholder={currentLanguage === 'ar' ? 'مسح رمز QR للمعلومات التلقائية للعميل' :
                                        currentLanguage === 'fr' ? 'Scanner le QR pour les informations automatiques du client' :
                                        'Scan QR for automatic client information'}
                            style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                            }}
                            autoFocus
                            onFocus={() => {
                              if (window.barcodeShortcutManager) {
                                window.barcodeShortcutManager.isBarcodeActive = true;
                                window.barcodeShortcutManager.setShortcutsEnabled(false);
                              }
                            }}
                            onBlur={() => {
                              setTimeout(() => {
                                if (window.barcodeShortcutManager && !window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)) {
                                  window.barcodeShortcutManager.isBarcodeActive = false;
                                  window.barcodeShortcutManager.setShortcutsEnabled(true);
                                }
                              }, 100);
                            }}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                const barcode = e.target.value.trim();
                                const foundRepair = repairsData.find(repair =>
                                  repair.repairBarcode === barcode &&
                                  (repair.status === 'waitingForClient' || repair.status === 'enAttenteClient')
                                );
                                if (foundRepair) {
                                  setSelectedRepair(foundRepair);
                                  showToast(t('repairFound', 'Repair found'), 'success');
                                  e.target.value = '';
                                } else {
                                  showToast(t('repairNotFound', 'Repair not found'), 'error');
                                }
                              }
                            }}
                          />
                          <span className="barcode-scanner-icon">📷</span>
                        </div>
                      </div>

                      {/* Manual Search Section */}
                      <div className="pickup-filter-section" style={{marginTop: '1.5rem'}}>
                        <div className="filter-header">
                          <h4>{t('orSearchManually', 'Or Search Manually')}</h4>
                        </div>
                        <div className="search-filters">
                          <input
                            type="text"
                            className="filter-input"
                            placeholder={t('searchByNamePhoneDate', 'Search by name, phone, or date...')}
                            value={repairFilter || ''}
                            onChange={(e) => setRepairFilter(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Right Column: Repairs Table */}
                    <div style={{flex: '2', minWidth: '600px'}}>
                      <div className="pickup-repairs-table-container recuperation-table-fixed">
                        {repairsData.filter(repair =>
                          (repair.status === 'waitingForClient' || repair.status === 'enAttenteClient') &&
                          (!repairFilter ||
                            repair.clientName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            repair.clientPhone.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            repair.deviceName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                            new Date(repair.createdAt).toLocaleDateString().includes(repairFilter) ||
                            repair.repairBarcode?.toLowerCase().includes(repairFilter.toLowerCase())
                          )
                        ).length === 0 ? (
                          <div className="empty-state">
                            <div className="empty-icon">📱</div>
                            <h4>{t('noRepairsReady', 'No repairs ready for pickup')}</h4>
                            <p>{t('checkRepairStatuses', 'Check repair statuses or complete repairs first')}</p>
                          </div>
                        ) : (
                          <div className="table-wrapper-fixed">
                            <table className="pickup-repairs-table modern-table recuperation-fixed-table">
                              <thead className="table-header-fixed">
                                <tr style={{height: '50px', backgroundColor: '#42F2F7', color: 'white'}}>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('clientName', 'Client Name')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('deviceNameHeader', 'Device Name')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('status', 'Status')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('partsPrice', 'Parts Price')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('interestRate', 'Interest Rate')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('totalAmount', 'Total Amount')}</th>
                                  <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle', backgroundColor: '#42F2F7', color: 'white'}}>{t('depositDate', 'Deposit Date')}</th>
                                </tr>
                              </thead>
                            </table>
                            <div className="table-body-scrollable repairs-table-container">
                              <table className="pickup-repairs-table modern-table recuperation-fixed-table">
                                <thead className="table-header-hidden">
                                  <tr style={{height: '50px'}}>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('clientName', 'Client Name')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('deviceNameHeader', 'Device Name')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('status', 'Status')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('partsPrice', 'Parts Price')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('interestRate', 'Interest Rate')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('totalAmount', 'Total Amount')}</th>
                                    <th style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>{t('depositDate', 'Deposit Date')}</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {repairsData.filter(repair =>
                                    (repair.status === 'waitingForClient' || repair.status === 'enAttenteClient') &&
                                    (!repairFilter ||
                                      repair.clientName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                      repair.clientPhone.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                      repair.deviceName.toLowerCase().includes(repairFilter.toLowerCase()) ||
                                      new Date(repair.createdAt).toLocaleDateString().includes(repairFilter) ||
                                      repair.repairBarcode?.toLowerCase().includes(repairFilter.toLowerCase())
                                    )
                                  ).slice(0, 3).map(repair => (
                                    <tr
                                      key={repair.id}
                                      className="pickup-repair-row clickable-row"
                                      style={{height: '50px'}}
                                      onClick={() => setSelectedRepair(repair)}
                                    >
                                      <td className="client-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <div className="client-info">
                                          <span className="client-name">{repair.clientName}</span>
                                          <span className="client-phone">{repair.clientPhone}</span>
                                        </div>
                                      </td>
                                      <td className="device-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className="device-name">{repair.deviceName}</span>
                                        <span className="device-type">{repair.deviceType}</span>
                                      </td>
                                      <td className="status-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className={`status-badge status-${repair.status}`}>
                                          {repair.status === 'waitingForClient' ?
                                            (currentLanguage === 'ar' ? 'في انتظار' :
                                             currentLanguage === 'fr' ? 'En attente' :
                                             'Waiting') :
                                            translateRepairStatus(repair.status)
                                          }
                                        </span>
                                      </td>
                                      <td className="parts-price-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className="parts-price">{formatPrice(repair.partsPrice || 0)}</span>
                                      </td>
                                      <td className="interest-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className="interest-rate">
                                          {repair.isFailedRepair || repair.status === 'enAttenteClient'
                                            ? formatPrice(repair.verificationPrice || 0)
                                            : formatPrice((parseFloat(repair.repairPrice || 0) - parseFloat(repair.partsPrice || 0)))
                                          }
                                        </span>
                                      </td>
                                      <td className="total-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className="total-amount">
                                          {repair.isFailedRepair || repair.status === 'enAttenteClient'
                                            ? formatPrice(repair.verificationPrice || 0)
                                            : formatPrice(repair.repairPrice || 0)
                                          }
                                        </span>
                                      </td>
                                      <td className="date-cell" style={{padding: '15px', fontSize: '0.9rem', verticalAlign: 'middle'}}>
                                        <span className="deposit-date">{new Date(repair.createdAt || repair.depositDate).toLocaleDateString()}</span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Process Pickup - Modern Design */}
              {selectedRepair && (
                <div className="modern-pickup-step">
                  {/* Modern Step Header */}
                  <div className="modern-pickup-header" style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '2rem',
                    padding: '1.5rem 2rem',
                    background: 'white',
                    borderRadius: '16px',
                    color: '#2c3e50',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                    border: '2px solid #e9ecef'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div className="step-number-pickup" style={{
                        width: '50px',
                        height: '50px',
                        borderRadius: '50%',
                        background: 'linear-gradient(135deg, #667eea, #764ba2)',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '1.5rem',
                        fontWeight: '800',
                        marginRight: currentLanguage === 'ar' ? '0' : '1rem',
                        marginLeft: currentLanguage === 'ar' ? '1rem' : '0'
                      }}>2</div>
                      <div className="step-info-pickup">
                        <h3 style={{
                          fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                          fontSize: '1.5rem',
                          fontWeight: '700',
                          margin: '0 0 0.3rem 0',
                          color: '#2c3e50'
                        }}>
                          {currentLanguage === 'ar' ? 'معالجة الاستلام' :
                           currentLanguage === 'fr' ? 'Traitement de la récupération' :
                           'Process Pickup'}
                        </h3>
                        <p style={{
                          fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                          fontSize: '1rem',
                          margin: '0',
                          color: '#6c757d'
                        }}>
                          {currentLanguage === 'ar' ? 'تأكيد استلام العميل والدفع' :
                           currentLanguage === 'fr' ? 'Confirmer la récupération et le paiement' :
                           'Confirm client pickup and payment'}
                        </p>
                      </div>
                    </div>
                    <button
                      className="btn-back-modern"
                      onClick={() => setSelectedRepair(null)}
                      style={{
                        background: 'linear-gradient(135deg, #6c757d, #495057)',
                        border: 'none',
                        borderRadius: '8px',
                        color: 'white',
                        padding: '0.5rem 1rem',
                        fontSize: '1rem',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                      }}
                      onMouseEnter={(e) => e.target.style.background = 'linear-gradient(135deg, #495057, #343a40)'}
                      onMouseLeave={(e) => e.target.style.background = 'linear-gradient(135deg, #6c757d, #495057)'}
                    >
                      ← {currentLanguage === 'ar' ? 'رجوع' :
                          currentLanguage === 'fr' ? 'Retour' :
                          'Back'}
                    </button>
                  </div>



                  {/* Modern Pickup Details Section */}
                  <div className="modern-pickup-details" style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '2rem',
                    marginBottom: '2rem'
                  }}>
                    {/* Left Column: Repair Details */}
                    <div className="repair-details-card" style={{
                      background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
                      borderRadius: '16px',
                      padding: '2rem',
                      border: '2px solid #667eea',
                      boxShadow: '0 8px 32px rgba(102, 126, 234, 0.15)'
                    }}>
                      <div className="card-header" style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginBottom: '1.5rem',
                        paddingBottom: '1rem',
                        borderBottom: '2px solid #f8f9fa'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div className="card-icon" style={{
                            width: '40px',
                            height: '40px',
                            borderRadius: '8px',
                            background: 'linear-gradient(135deg, #667eea, #764ba2)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '1.2rem',
                            color: 'white',
                            marginRight: currentLanguage === 'ar' ? '0' : '1rem',
                            marginLeft: currentLanguage === 'ar' ? '1rem' : '0'
                          }}>📋</div>
                          <div>
                            <h4 style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1.2rem',
                              fontWeight: '700',
                              margin: '0',
                              color: '#667eea'
                            }}>
                              {currentLanguage === 'ar' ? 'تفاصيل الإصلاح' :
                               currentLanguage === 'fr' ? 'Détails de la réparation' :
                               'Repair Details'}
                            </h4>
                          </div>
                        </div>

                        {/* Print Invoice Button */}
                        <button
                          className="print-invoice-btn"
                          onClick={() => {
                            // Print client invoice following PrintThermalInvoice.md specifications
                            try {
                              // Get store settings for logo and tax
                              const storeSettings = JSON.parse(localStorage.getItem('storeSettings') || '{}');
                              const taxRate = parseFloat(storeSettings.taxRate || 19);

                              // Create repair data for printing
                              const repairData = {
                                id: selectedRepair.id,
                                clientName: selectedRepair.clientName,
                                clientPhone: selectedRepair.clientPhone,
                                deviceName: selectedRepair.deviceName,
                                problemType: selectedRepair.problemType || selectedRepair.problemDescription || 'Réparation générale',
                                repairPrice: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                  ? parseFloat(selectedRepair.verificationPrice || 0)
                                  : parseFloat(selectedRepair.repairPrice || 0),
                                partsPrice: parseFloat(selectedRepair.partsPrice || 0),
                                interestRate: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                  ? parseFloat(selectedRepair.verificationPrice || 0)
                                  : (parseFloat(selectedRepair.repairPrice || 0) - parseFloat(selectedRepair.partsPrice || 0)),
                                totalPrice: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                  ? parseFloat(selectedRepair.verificationPrice || 0)
                                  : parseFloat(selectedRepair.repairPrice || 0),
                                status: selectedRepair.status,
                                date: selectedRepair.date || new Date().toISOString(),
                                repairBarcode: selectedRepair.repairBarcode || selectedRepair.id,
                                storeName: storeSettings.storeName || 'iRepair DZ',
                                storePhone: storeSettings.storePhone || '0551930589',
                                storeAddress: storeSettings.storeAddress || '',
                                storeLogo: storeSettings.storeLogo || null
                              };

                              // Calculate tax amount
                              const subtotal = repairData.totalPrice;
                              const taxAmount = (subtotal * taxRate) / 100;
                              const finalTotal = subtotal + taxAmount;

                              // Create enhanced thermal printer invoice following PrintThermalInvoice.md
                              const isRTL = currentLanguage === 'ar';
                              const invoiceHTML = `
                                <!DOCTYPE html>
                                <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${currentLanguage}">
                                <head>
                                  <meta charset="UTF-8">
                                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                                  <title>${currentLanguage === 'ar' ? 'فاتورة العميل' : currentLanguage === 'fr' ? 'Facture Client' : 'Client Invoice'}</title>
                                  <style>
                                    @page {
                                      size: 80mm auto;
                                      margin: 0;
                                    }

                                    * {
                                      margin: 0;
                                      padding: 0;
                                      box-sizing: border-box;
                                    }

                                    body {
                                      font-family: 'Arial Black', Arial, sans-serif;
                                      font-size: 14px;
                                      font-weight: 900;
                                      line-height: 1.4;
                                      color: #000;
                                      background: #fff;
                                      width: 74mm;
                                      margin: 0 auto;
                                      padding: 4mm;
                                      direction: ${isRTL ? 'rtl' : 'ltr'};
                                      text-align: center;
                                    }

                                    .thermal-receipt {
                                      width: 100%;
                                    }

                                    .store-header {
                                      text-align: center;
                                      margin-bottom: 4mm;
                                      padding-bottom: 3mm;
                                      border-bottom: 3px solid #000;
                                    }

                                    .store-logo {
                                      max-width: 40mm;
                                      max-height: 15mm;
                                      margin-bottom: 2mm;
                                    }

                                    .store-name {
                                      font-size: 18px;
                                      font-weight: 900;
                                      text-transform: uppercase;
                                      margin-bottom: 1mm;
                                    }

                                    .store-info {
                                      font-size: 12px;
                                      font-weight: 700;
                                    }

                                    .invoice-header {
                                      font-size: 16px;
                                      font-weight: 900;
                                      text-transform: uppercase;
                                      margin: 4mm 0;
                                      padding: 2mm;
                                      background: #f0f0f0;
                                      border: 2px solid #000;
                                    }

                                    .thermal-info {
                                      text-align: ${isRTL ? 'right' : 'left'};
                                      margin: 3mm 0;
                                      font-size: 14px;
                                      font-weight: 700;
                                    }

                                    .thermal-info div {
                                      margin: 2mm 0;
                                      display: flex;
                                      justify-content: space-between;
                                      align-items: center;
                                    }

                                    .thermal-info .label {
                                      font-weight: 900;
                                      min-width: 35mm;
                                    }

                                    .thermal-info .value {
                                      font-weight: 700;
                                      text-align: ${isRTL ? 'left' : 'right'};
                                    }

                                    .service-section {
                                      margin: 4mm 0;
                                      padding: 3mm;
                                      border: 2px solid #000;
                                      background: #f8f8f8;
                                    }

                                    .service-title {
                                      font-size: 14px;
                                      font-weight: 900;
                                      text-transform: uppercase;
                                      margin-bottom: 2mm;
                                      text-align: center;
                                    }

                                    .tax-section {
                                      margin: 3mm 0;
                                      font-size: 14px;
                                      font-weight: 700;
                                    }

                                    .thermal-total {
                                      font-size: 18px;
                                      font-weight: 900;
                                      text-align: center;
                                      margin: 4mm 0;
                                      padding: 3mm;
                                      border: 3px solid #000;
                                      background: #e0e0e0;
                                      text-transform: uppercase;
                                    }

                                    .thermal-footer {
                                      text-align: center;
                                      margin-top: 4mm;
                                      font-size: 12px;
                                      font-weight: 700;
                                      border-top: 2px solid #000;
                                      padding-top: 3mm;
                                    }

                                    .separator {
                                      border-top: 2px dashed #000;
                                      margin: 3mm 0;
                                    }

                                    .thick-separator {
                                      border-top: 3px solid #000;
                                      margin: 4mm 0;
                                    }
                                  </style>
                                </head>
                                <body>
                                  <div class="thermal-receipt">
                                    <!-- Store Header with Logo -->
                                    <div class="store-header">
                                      ${repairData.storeLogo ?
                                        `<img src="${repairData.storeLogo}" alt="Logo" class="store-logo" />` :
                                        `<div style="font-size: 24px; margin-bottom: 2mm;">🏪</div>`
                                      }
                                      <div class="store-name">${repairData.storeName}</div>
                                      <div class="store-info">
                                        ${repairData.storePhone ? `📞 ${repairData.storePhone}` : '📞 0551930589'}
                                        ${repairData.storeAddress ? `<br/>${repairData.storeAddress}` : ''}
                                      </div>
                                    </div>

                                    <!-- Invoice Header -->
                                    <div class="invoice-header">
                                      ${currentLanguage === 'ar' ? 'فاتورة العميل' : currentLanguage === 'fr' ? 'FACTURE CLIENT' : 'CLIENT INVOICE'}
                                    </div>

                                    <!-- Invoice Details -->
                                    <div class="thermal-info">
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'رقم الفاتورة:' : currentLanguage === 'fr' ? 'N° Facture:' : 'Invoice No:'}</span>
                                        <span class="value">${repairData.id}</span>
                                      </div>
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'التاريخ:' : currentLanguage === 'fr' ? 'Date:' : 'Date:'}</span>
                                        <span class="value">${new Date().toLocaleDateString()}</span>
                                      </div>
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'الوقت:' : currentLanguage === 'fr' ? 'Heure:' : 'Time:'}</span>
                                        <span class="value">${new Date().toLocaleTimeString()}</span>
                                      </div>
                                    </div>

                                    <div class="separator"></div>

                                    <!-- Client Information -->
                                    <div class="thermal-info">
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'العميل:' : currentLanguage === 'fr' ? 'Client:' : 'Client:'}</span>
                                        <span class="value">${repairData.clientName}</span>
                                      </div>
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'الهاتف:' : currentLanguage === 'fr' ? 'Téléphone:' : 'Phone:'}</span>
                                        <span class="value">${repairData.clientPhone}</span>
                                      </div>
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'الجهاز:' : currentLanguage === 'fr' ? 'Appareil:' : 'Device:'}</span>
                                        <span class="value">${repairData.deviceName}</span>
                                      </div>
                                      <div>
                                        <span class="label">${currentLanguage === 'ar' ? 'المشكلة:' : currentLanguage === 'fr' ? 'Problème:' : 'Problem:'}</span>
                                        <span class="value">${repairData.problemType}</span>
                                      </div>
                                    </div>

                                    <div class="thick-separator"></div>

                                    <!-- Service Section -->
                                    <div class="service-section">
                                      <div class="service-title">
                                        ${currentLanguage === 'ar' ? 'تفاصيل الخدمة' : currentLanguage === 'fr' ? 'DÉTAILS DU SERVICE' : 'SERVICE DETAILS'}
                                      </div>
                                      <div class="thermal-info">
                                        <div>
                                          <span class="label">${currentLanguage === 'ar' ? 'خدمة الإصلاح:' : currentLanguage === 'fr' ? 'Service de réparation:' : 'Repair Service:'}</span>
                                          <span class="value">${Math.round(repairData.totalPrice)} ${currentLanguage === 'ar' ? 'د.ج' : 'DZD'}</span>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Tax Calculation -->
                                    <div class="tax-section">
                                      <div class="thermal-info">
                                        <div>
                                          <span class="label">${currentLanguage === 'ar' ? 'المجموع الفرعي:' : currentLanguage === 'fr' ? 'Sous-total:' : 'Subtotal:'}</span>
                                          <span class="value">${Math.round(subtotal)} ${currentLanguage === 'ar' ? 'د.ج' : 'DZD'}</span>
                                        </div>
                                        <div>
                                          <span class="label">${currentLanguage === 'ar' ? `الضريبة (${taxRate}%):` : currentLanguage === 'fr' ? `TVA (${taxRate}%):` : `Tax (${taxRate}%):`}</span>
                                          <span class="value">${Math.round(taxAmount)} ${currentLanguage === 'ar' ? 'د.ج' : 'DZD'}</span>
                                        </div>
                                      </div>
                                    </div>

                                    <!-- Final Total -->
                                    <div class="thermal-total">
                                      ${currentLanguage === 'ar' ? 'المجموع النهائي:' : currentLanguage === 'fr' ? 'TOTAL FINAL:' : 'FINAL TOTAL:'}<br/>
                                      ${Math.round(subtotal + taxAmount)} ${currentLanguage === 'ar' ? 'د.ج' : 'DZD'}
                                    </div>

                                    <!-- Footer -->
                                    <div class="thermal-footer">
                                      <div style="font-weight: 900; font-size: 14px; margin-bottom: 2mm;">
                                        ${currentLanguage === 'ar' ? 'شكراً لثقتكم بنا' : currentLanguage === 'fr' ? 'MERCI POUR VOTRE CONFIANCE' : 'THANK YOU FOR YOUR TRUST'}
                                      </div>
                                      <div style="margin: 2mm 0;">
                                        ${currentLanguage === 'ar' ? 'تم التطوير بواسطة' : currentLanguage === 'fr' ? 'Développé par' : 'Developed by'} <strong>iCode DZ</strong>
                                      </div>
                                      <div style="font-size: 10px; margin-top: 2mm;">
                                        ${currentLanguage === 'ar' ? 'طُبعت في:' : currentLanguage === 'fr' ? 'Imprimé le:' : 'Printed on:'} ${new Date().toLocaleString()}
                                      </div>
                                    </div>
                                  </div>
                                </body>
                                </html>
                              `;

                              // Open print window
                              const printWindow = window.open('', '_blank');
                              printWindow.document.write(invoiceHTML);
                              printWindow.document.close();
                              printWindow.focus();
                              printWindow.print();

                              showToast(
                                currentLanguage === 'ar' ? 'تم فتح نافذة الطباعة' :
                                currentLanguage === 'fr' ? 'Fenêtre d\'impression ouverte' :
                                'Print window opened',
                                'success'
                              );
                            } catch (error) {
                              console.error('Printing error:', error);
                              showToast(
                                currentLanguage === 'ar' ? 'خطأ في الطباعة' :
                                currentLanguage === 'fr' ? 'Erreur d\'impression' :
                                'Printing error',
                                'error'
                              );
                            }
                          }}
                          style={{
                            padding: '0.5rem 1rem',
                            background: 'linear-gradient(135deg, #28a745, #20c997)',
                            border: 'none',
                            borderRadius: '8px',
                            color: 'white',
                            fontSize: '0.9rem',
                            fontWeight: '600',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            boxShadow: '0 2px 8px rgba(40, 167, 69, 0.3)'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.transform = 'translateY(-1px)';
                            e.target.style.boxShadow = '0 4px 12px rgba(40, 167, 69, 0.4)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.transform = 'translateY(0)';
                            e.target.style.boxShadow = '0 2px 8px rgba(40, 167, 69, 0.3)';
                          }}
                        >
                          <span style={{ fontSize: '1rem' }}>🖨️</span>
                          <span>
                            {currentLanguage === 'ar' ? 'طباعة الفاتورة' :
                             currentLanguage === 'fr' ? 'Imprimer facture' :
                             'Print Invoice'}
                          </span>
                        </button>
                      </div>



                      {/* Modern Pricing Grid */}
                      <div className="pricing-grid" style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        <div className="price-item" style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '0.75rem',
                          background: 'linear-gradient(135deg, #fff3cd, #ffeaa7)',
                          borderRadius: '8px',
                          border: '2px solid #ffc107'
                        }}>
                          <span style={{ fontSize: '1.2rem', marginRight: currentLanguage === 'ar' ? '0' : '0.75rem', marginLeft: currentLanguage === 'ar' ? '0.75rem' : '0' }}>💰</span>
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '0.8rem',
                              fontWeight: '600',
                              color: '#856404',
                              marginBottom: '0.2rem'
                            }}>
                              {currentLanguage === 'ar' ? 'سعر الإصلاح' : currentLanguage === 'fr' ? 'Prix de réparation' : 'Repair Price'}
                            </div>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1rem',
                              color: '#856404',
                              fontWeight: '700'
                            }}>{formatPrice(selectedRepair.repairPrice || 0)}</div>
                          </div>
                        </div>

                        <div className="price-item" style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '0.75rem',
                          background: 'linear-gradient(135deg, #d1ecf1, #bee5eb)',
                          borderRadius: '8px',
                          border: '2px solid #17a2b8'
                        }}>
                          <span style={{ fontSize: '1.2rem', marginRight: currentLanguage === 'ar' ? '0' : '0.75rem', marginLeft: currentLanguage === 'ar' ? '0.75rem' : '0' }}>🔧</span>
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '0.8rem',
                              fontWeight: '600',
                              color: '#0c5460',
                              marginBottom: '0.2rem'
                            }}>
                              {currentLanguage === 'ar' ? 'سعر القطع' : currentLanguage === 'fr' ? 'Prix des pièces' : 'Parts Price'}
                            </div>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1rem',
                              color: '#0c5460',
                              fontWeight: '700'
                            }}>{formatPrice(selectedRepair.partsPrice || 0)}</div>
                          </div>
                        </div>
                      </div>

                      {/* Interest Rate & Total Amount - Combined Row */}
                      <div className="financial-summary-row" style={{
                        display: 'grid',
                        gridTemplateColumns: '1fr 1fr',
                        gap: '1.5rem',
                        marginBottom: '1rem'
                      }}>
                        {/* Interest Rate */}
                        <div className="interest-rate-card" style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '1.2rem',
                          background: 'linear-gradient(135deg, #e8f5e8, #d4edda)',
                          borderRadius: '12px',
                          border: '2px solid #27ae60'
                        }}>
                          <span style={{
                            fontSize: '1.5rem',
                            marginRight: currentLanguage === 'ar' ? '0' : '0.75rem',
                            marginLeft: currentLanguage === 'ar' ? '0.75rem' : '0'
                          }}>📈</span>
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '0.9rem',
                              fontWeight: '600',
                              color: '#27ae60',
                              marginBottom: '0.3rem'
                            }}>
                              {currentLanguage === 'ar' ? 'معدل الفائدة' : currentLanguage === 'fr' ? 'Taux d\'intérêt' : 'Interest Rate'}
                            </div>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1.2rem',
                              color: '#27ae60',
                              fontWeight: '800'
                            }}>
                              {selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                ? formatPrice(selectedRepair.verificationPrice || 0)
                                : formatPrice((parseFloat(selectedRepair.repairPrice || 0) - parseFloat(selectedRepair.partsPrice || 0)))
                              }
                            </div>
                          </div>
                        </div>

                        {/* Total Amount */}
                        <div className="total-amount-card" style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '1.2rem',
                          background: 'linear-gradient(135deg, #667eea, #764ba2)',
                          borderRadius: '12px',
                          border: 'none',
                          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.4)'
                        }}>
                          <span style={{
                            fontSize: '1.8rem',
                            marginRight: currentLanguage === 'ar' ? '0' : '0.75rem',
                            marginLeft: currentLanguage === 'ar' ? '0.75rem' : '0',
                            color: 'white'
                          }}>💎</span>
                          <div style={{ flex: 1 }}>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1rem',
                              fontWeight: '600',
                              color: 'rgba(255, 255, 255, 0.9)',
                              marginBottom: '0.3rem'
                            }}>
                              {currentLanguage === 'ar' ? 'المبلغ الإجمالي' : currentLanguage === 'fr' ? 'Montant Total' : 'Total Amount'}
                            </div>
                            <div style={{
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              fontSize: '1.5rem',
                              color: 'white',
                              fontWeight: '900'
                            }}>
                              {selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                ? formatPrice(selectedRepair.verificationPrice || 0)
                                : formatPrice(selectedRepair.repairPrice || 0)
                              }
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right Column: Final Price & Actions */}
                    <div className="pickup-actions-card" style={{
                      background: 'white',
                      borderRadius: '16px',
                      padding: '2rem',
                      border: '2px solid #e9ecef',
                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '1.5rem'
                    }}>
                      {/* Final Price Section */}
                      <div className="final-price-section" style={{
                        padding: '1.5rem',
                        background: 'linear-gradient(135deg, #fff3cd, #ffeaa7)',
                        borderRadius: '12px',
                        border: '2px solid #ffc107'
                      }}>
                        <div className="price-header" style={{
                          display: 'flex',
                          alignItems: 'center',
                          marginBottom: '1rem'
                        }}>
                          <span style={{
                            fontSize: '1.5rem',
                            marginRight: currentLanguage === 'ar' ? '0' : '0.75rem',
                            marginLeft: currentLanguage === 'ar' ? '0.75rem' : '0'
                          }}>💰</span>
                          <h4 style={{
                            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                            fontSize: '1.2rem',
                            fontWeight: '700',
                            margin: '0',
                            color: '#856404'
                          }}>
                            {currentLanguage === 'ar' ? 'السعر النهائي' :
                             currentLanguage === 'fr' ? 'Prix Final' :
                             'Final Price'}
                          </h4>
                        </div>
                        <div className="price-input-container" style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem'
                        }}>
                          <input
                            id={`finalPrice-${selectedRepair.id}`}
                            type="number"
                            className="modern-price-input"
                            placeholder={currentLanguage === 'ar' ? 'أدخل السعر النهائي' :
                                        currentLanguage === 'fr' ? 'Entrer le prix final' :
                                        'Enter final price'}
                            defaultValue={
                              selectedRepair.totalPrice ||
                              (selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                                ? (selectedRepair.verificationPrice || 0)
                                : parseFloat(selectedRepair.repairPrice || 0))
                            }
                            style={{
                              flex: 1,
                              padding: '0.75rem 1rem',
                              border: '2px solid #ffc107',
                              borderRadius: '8px',
                              fontSize: '1.1rem',
                              fontWeight: '600',
                              textAlign: 'center',
                              fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                              background: 'white',
                              color: '#856404'
                            }}
                          />
                          <span style={{
                            fontSize: '1.1rem',
                            fontWeight: '600',
                            color: '#856404',
                            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                          }}>{currentLanguage === 'ar' ? 'د.ج' : 'DZD'}</span>
                        </div>
                      </div>

                      {/* Status Display */}
                      <div className="status-display" style={{
                        padding: '1rem',
                        background: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                          ? 'linear-gradient(135deg, #f8d7da, #f5c6cb)'
                          : 'linear-gradient(135deg, #d1ecf1, #bee5eb)',
                        borderRadius: '8px',
                        border: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                          ? '2px solid #dc3545'
                          : '2px solid #17a2b8',
                        textAlign: 'center'
                      }}>
                        <span style={{
                          fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                          fontSize: '1rem',
                          fontWeight: '600',
                          color: selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                            ? '#721c24'
                            : '#0c5460'
                        }}>
                          {currentLanguage === 'ar' ? 'الحالة' : currentLanguage === 'fr' ? 'Statut' : 'Status'}: {' '}
                          {selectedRepair.status === 'waitingForClient' ?
                            (currentLanguage === 'ar' ? 'في انتظار العميل' :
                             currentLanguage === 'fr' ? 'En attente du client' :
                             'Waiting for Client') :
                            translateRepairStatus(selectedRepair.status)
                          }
                        </span>
                      </div>

                      {/* Modern Action Button */}
                      <div className="modern-action-section" style={{
                        marginTop: 'auto'
                      }}>
                        <button
                          className="modern-confirm-button"
                          onClick={() => {
                            const finalPriceInput = document.getElementById(`finalPrice-${selectedRepair.id}`);
                            const finalPrice = finalPriceInput?.value ? parseFloat(finalPriceInput.value) : undefined;
                            processPickup(selectedRepair, finalPrice);
                          }}
                          style={{
                            width: '100%',
                            padding: '1.2rem 2rem',
                            background: 'linear-gradient(135deg, #28a745, #20c997)',
                            border: 'none',
                            borderRadius: '12px',
                            color: 'white',
                            fontSize: '1.1rem',
                            fontWeight: '700',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                            boxShadow: '0 4px 15px rgba(40, 167, 69, 0.3)',
                            fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            gap: '0.75rem'
                          }}
                          onMouseEnter={(e) => {
                            e.target.style.transform = 'translateY(-2px)';
                            e.target.style.boxShadow = '0 6px 20px rgba(40, 167, 69, 0.4)';
                          }}
                          onMouseLeave={(e) => {
                            e.target.style.transform = 'translateY(0)';
                            e.target.style.boxShadow = '0 4px 15px rgba(40, 167, 69, 0.3)';
                          }}
                        >
                          <span style={{ fontSize: '1.3rem' }}>✅</span>
                          <span>
                            {currentLanguage === 'ar' ? 'تأكيد التسليم' :
                             currentLanguage === 'fr' ? 'Confirmer la livraison' :
                             'Confirm Delivery'}
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Problem Manager Modal */}
      {showProblemManagerModal && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowProblemManagerModal(false)}>
          <div className={`modal-content problem-manager-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header problem-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">🔧</div>
                <div className="header-text">
                  <h2>{t('problemManager', 'إدارة المشاكل')}</h2>
                  <p>{t('addCustomProblem', 'إضافة مشكلة مخصصة')}</p>
                </div>
              </div>
              <button className="modal-close-btn" onClick={() => setShowProblemManagerModal(false)}>✕</button>
            </div>

            <div className="problem-form-container">
              <div className="form-section">
                <h3 className="section-title">
                  <span className="section-icon">➕</span>
                  {editingProblem ?
                    (currentLanguage === 'ar' ? 'تعديل المشكلة' :
                     currentLanguage === 'fr' ? 'Modifier le Problème' :
                     'Edit Problem') :
                    (currentLanguage === 'ar' ? 'إضافة مشكلة جديدة' :
                     currentLanguage === 'fr' ? 'Ajouter un Nouveau Problème' :
                     'Add New Problem')
                  }
                </h3>
                <div className="form-grid">
                  <div className="form-field">
                    <label className="modern-label">{t('problemName', 'اسم المشكلة')}</label>
                    <input
                      type="text"
                      className="modern-input"
                      value={newProblemForm.name}
                      onChange={(e) => setNewProblemForm({...newProblemForm, name: e.target.value})}
                      placeholder={t('enterProblemName', 'أدخل اسم المشكلة')}
                      required
                    />
                  </div>
                  <div className="form-field">
                    <label className="modern-label">{t('problemDescription', 'وصف المشكلة')}</label>
                    <textarea
                      className="modern-textarea"
                      value={newProblemForm.description}
                      onChange={(e) => setNewProblemForm({...newProblemForm, description: e.target.value})}
                      placeholder={t('enterProblemDescription', 'أدخل وصف المشكلة')}
                      rows="3"
                      required
                    />
                  </div>
                </div>
                <div className="form-actions">
                  <button
                    className="btn-modern btn-primary"
                    onClick={() => {
                      if (!newProblemForm.name.trim() || !newProblemForm.description.trim()) {
                        showToast(t('fillAllFields', 'يرجى ملء جميع الحقول'), 'error');
                        return;
                      }

                      if (editingProblem) {
                        // Update existing problem
                        const updatedProblems = customProblems.map(p =>
                          p.id === editingProblem.id ?
                          {...p, name: newProblemForm.name, description: newProblemForm.description} :
                          p
                        );
                        saveCustomProblems(updatedProblems);
                        showToast(t('problemUpdated', 'تم تحديث المشكلة بنجاح'), 'success');
                        setEditingProblem(null);
                      } else {
                        // Add new problem
                        const newProblem = addCustomProblem(newProblemForm);
                        showToast(t('problemAdded', 'تم إضافة المشكلة بنجاح'), 'success');

                        // Auto-select the new problem in the repair form
                        setNewRepairForm({
                          ...newRepairForm,
                          problemType: `custom_${newProblem.id}`,
                          problemDescription: newProblem.description
                        });
                      }

                      setNewProblemForm({ name: '', description: '' });
                      setShowProblemManagerModal(false);
                    }}
                  >
                    <span className="btn-icon">{editingProblem ? '✏️' : '➕'}</span>
                    <span className="btn-text">
                      {editingProblem ? t('updateProblem', 'تحديث المشكلة') : t('addProblem', 'إضافة المشكلة')}
                    </span>
                  </button>
                  <button
                    className="btn-modern btn-secondary"
                    onClick={() => {
                      setShowProblemManagerModal(false);
                      setEditingProblem(null);
                      setNewProblemForm({ name: '', description: '' });
                    }}
                  >
                    <span className="btn-icon">❌</span>
                    <span className="btn-text">{t('cancel', 'إلغاء')}</span>
                  </button>
                </div>
              </div>

              {/* Existing Problems List */}
              {customProblems.length > 0 && (
                <div className="form-section">
                  <h3 className="section-title">
                    <span className="section-icon">📋</span>
                    {currentLanguage === 'ar' ? 'المشاكل المخصصة' :
                     currentLanguage === 'fr' ? 'Problèmes Personnalisés' :
                     'Custom Problems'}
                  </h3>
                  <div className="problems-list">
                    {customProblems.map(problem => (
                      <div key={problem.id} className="problem-item">
                        <div className="problem-info">
                          <h4 className="problem-name">{problem.name}</h4>
                          <p className="problem-description">{problem.description}</p>
                        </div>
                        <div className="problem-actions">
                          <button
                            className="btn btn-warning btn-xs"
                            onClick={() => {
                              setEditingProblem(problem);
                              setNewProblemForm({
                                name: problem.name,
                                description: problem.description
                              });
                            }}
                            title={t('editProblem', 'تعديل المشكلة')}
                          >
                            ✏️
                          </button>
                          <button
                            className="btn btn-danger btn-xs"
                            onClick={() => {
                              if (window.confirm(t('confirmDeleteProblem', 'هل أنت متأكد من حذف هذه المشكلة؟'))) {
                                const updatedProblems = customProblems.filter(p => p.id !== problem.id);
                                saveCustomProblems(updatedProblems);
                                showToast(t('problemDeleted', 'تم حذف المشكلة'), 'success');
                              }
                            }}
                            title={t('deleteProblem', 'حذف المشكلة')}
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* QR Code Modal - Clean Design */}
      {showQRModal && selectedQRCode && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowQRModal(false)}>
          <div
            className={`modal-content qr-modal-clean lang-${currentLanguage}`}
            onClick={(e) => e.stopPropagation()}
            style={{
              direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
              textAlign: currentLanguage === 'ar' ? 'right' : 'left'
            }}
          >
            {/* Clean Header */}
            <div className={`qr-modal-header-clean ${currentLanguage !== 'ar' ? 'header-ltr' : 'header-rtl'}`}>
              <div className="header-content-clean">
                <div className="header-icon-clean">📱</div>
                <div className="header-text-clean">
                  <h2 style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {currentLanguage === 'ar' ? 'رمز QR للإصلاح' :
                     currentLanguage === 'fr' ? 'Code QR de Réparation' :
                     'Repair QR Code'}
                  </h2>
                  <p style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {selectedQRCode.repair.id} - {selectedQRCode.repair.clientName}
                  </p>
                </div>
              </div>
              <button
                className="modal-close-btn-clean"
                onClick={() => setShowQRModal(false)}
                style={{
                  position: 'absolute',
                  top: '15px',
                  right: currentLanguage === 'ar' ? 'auto' : '15px',
                  left: currentLanguage === 'ar' ? '15px' : 'auto'
                }}
              >
                ✕
              </button>
            </div>

            {/* Clean Content Layout */}
            <div className="qr-modal-content-clean">
              <div className="qr-display-section-clean">
                {/* QR Code Display */}
                <div className="qr-code-container-clean">
                  <div className="qr-code-large-clean">
                    <img
                      src={selectedQRCode.qrCode}
                      alt="QR Code"
                      style={{
                        width: '280px',
                        height: '280px',
                        borderRadius: '12px',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                  </div>
                </div>

                {/* Repair Information */}
                <div className="qr-info-clean">
                  <h3 style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left',
                    marginBottom: '20px',
                    color: '#2c3e50',
                    fontSize: '1.3rem'
                  }}>
                    {currentLanguage === 'ar' ? 'تفاصيل الإصلاح' :
                     currentLanguage === 'fr' ? 'Détails de la Réparation' :
                     'Repair Details'}
                  </h3>

                  <div className="qr-details-grid-clean">
                    <div className="detail-row-clean">
                      <span className="detail-label-clean">
                        {currentLanguage === 'ar' ? 'رقم الإصلاح:' :
                         currentLanguage === 'fr' ? 'N° de Réparation:' :
                         'Repair ID:'}
                      </span>
                      <span className="detail-value-clean">{selectedQRCode.repair.id}</span>
                    </div>
                    <div className="detail-row-clean">
                      <span className="detail-label-clean">
                        {currentLanguage === 'ar' ? 'اسم العميل:' :
                         currentLanguage === 'fr' ? 'Nom du Client:' :
                         'Client Name:'}
                      </span>
                      <span className="detail-value-clean">{selectedQRCode.repair.clientName}</span>
                    </div>
                    <div className="detail-row-clean">
                      <span className="detail-label-clean">
                        {currentLanguage === 'ar' ? 'اسم الجهاز:' :
                         currentLanguage === 'fr' ? 'Nom de l\'Appareil:' :
                         'Device Name:'}
                      </span>
                      <span className="detail-value-clean">{selectedQRCode.repair.deviceName}</span>
                    </div>
                    <div className="detail-row-clean">
                      <span className="detail-label-clean">
                        {currentLanguage === 'ar' ? 'الحالة:' :
                         currentLanguage === 'fr' ? 'Statut:' :
                         'Status:'}
                      </span>
                      <span className="detail-value-clean">{translateRepairStatus(selectedQRCode.repair.status)}</span>
                    </div>
                    <div className="detail-row-clean">
                      <span className="detail-label-clean">
                        {currentLanguage === 'ar' ? 'سعر الإصلاح:' :
                         currentLanguage === 'fr' ? 'Prix de Réparation:' :
                         'Repair Price:'}
                      </span>
                      <span className="detail-value-clean">{formatPrice(selectedQRCode.repair.repairPrice)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Clean Action Buttons */}
              <div className="qr-actions-clean">
                <button
                  className="btn-modern-clean btn-primary-clean"
                  onClick={async () => {
                    try {
                      // Print invoice/facture instead of ticket
                      const success = await RepairThermalPrinter.printClientPickupReceipt(selectedQRCode.repair, {
                        language: currentLanguage,
                        showToast: showToast
                      });

                      if (success) {
                        showToast(
                          `🧾 ${currentLanguage === 'ar' ? 'طباعة الفاتورة' :
                                 currentLanguage === 'fr' ? 'Impression de la Facture' :
                                 'Printing Invoice'} ${selectedQRCode.repair.id}`,
                          'success',
                          2000
                        );
                      } else {
                        throw new Error('Print failed');
                      }
                    } catch (error) {
                      console.error('Error printing invoice:', error);
                      showToast(
                        `❌ ${currentLanguage === 'ar' ? 'خطأ في طباعة الفاتورة' :
                               currentLanguage === 'fr' ? 'Erreur d\'impression de la facture' :
                               'Error printing invoice'}`,
                        'error',
                        3000
                      );
                    }
                  }}
                  style={{
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                >
                  <span className="btn-icon-clean">🧾</span>
                  <span className="btn-text-clean">
                    {currentLanguage === 'ar' ? 'طباعة الفاتورة' :
                     currentLanguage === 'fr' ? 'Imprimer Facture' :
                     'Print Invoice'}
                  </span>
                </button>
                <button
                  className="btn-modern-clean btn-secondary-clean"
                  onClick={() => setShowQRModal(false)}
                  style={{
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                >
                  <span className="btn-icon-clean">❌</span>
                  <span className="btn-text-clean">
                    {currentLanguage === 'ar' ? 'إغلاق' :
                     currentLanguage === 'fr' ? 'Fermer' :
                     'Close'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Supplier Payment Modal */}
      {showSupplierpartsPayment && selectedSupplierparts && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowSupplierpartsPayment(false)}>
          <div className={`modal-content supplier-payment-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header supplier-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">💰</div>
                <div className="header-text">
                  <h2>{currentLanguage === 'ar' ? 'دفع للمورد' : currentLanguage === 'fr' ? 'Payer le Fournisseur' : 'Pay Supplier'}</h2>
                  <p>{selectedSupplierparts}</p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={() => setShowSupplierpartsPayment(false)}>×</button>
            </div>

            <div className="supplier-payment-content">
              <div className="payment-summary">
                <h3>{currentLanguage === 'ar' ? 'ملخص المعاملات' : currentLanguage === 'fr' ? 'Résumé des Transactions' : 'Transaction Summary'}</h3>
                <div className="summary-cards">
                  <div className="summary-card">
                    <span className="card-label">{currentLanguage === 'ar' ? 'إجمالي المبلغ' : currentLanguage === 'fr' ? 'Montant Total' : 'Total Amount'}</span>
                    <span className="card-value">{formatPrice(getSupplierTransactions(selectedSupplierparts).reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0))}</span>
                  </div>
                  <div className="summary-card pending">
                    <span className="card-label">{currentLanguage === 'ar' ? 'المبلغ المستحق' : currentLanguage === 'fr' ? 'Montant Dû' : 'Amount Due'}</span>
                    <span className="card-value">{formatPrice(getSupplierTotalCredit(selectedSupplierparts))}</span>
                  </div>
                </div>
              </div>

              <div className="payment-form">
                <div className="form-field">
                  <label>{currentLanguage === 'ar' ? 'مبلغ الدفع' : currentLanguage === 'fr' ? 'Montant du Paiement' : 'Payment Amount'}</label>
                  <input
                    type="number"
                    value={paymentAmountparts}
                    onChange={(e) => setPaymentAmountparts(e.target.value)}
                    placeholder={currentLanguage === 'ar' ? 'أدخل المبلغ' : currentLanguage === 'fr' ? 'Entrez le montant' : 'Enter amount'}
                    className="modern-input"
                  />
                </div>
              </div>

              <div className="payment-actions">
                <button
                  className="btn-modern btn-primary"
                  onClick={() => {
                    printSupplierTransactions(selectedSupplierparts);
                  }}
                >
                  <span className="btn-icon">🖨️</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'طباعة المعاملات' : currentLanguage === 'fr' ? 'Imprimer les Transactions' : 'Print Transactions'}</span>
                </button>
                <button
                  className="btn-modern btn-success"
                  onClick={() => {
                    if (paymentAmountparts && parseFloat(paymentAmountparts) > 0) {
                      const paymentAmount = parseFloat(paymentAmountparts);

                      // Get unpaid transactions for this supplier
                      const unpaidTransactions = suppliersPartsReparationData.filter(
                        transaction => transaction.supplierName === selectedSupplierparts &&
                                     transaction.status === 'credit' &&
                                     !transaction.paid
                      );

                      // Sort by date (oldest first)
                      unpaidTransactions.sort((a, b) => new Date(a.date) - new Date(b.date));

                      let remainingPayment = paymentAmount;
                      const updatedData = [...suppliersPartsReparationData];

                      // Create a payment record
                      const paymentRecord = {
                        id: 'PAY-' + Date.now(),
                        supplierName: selectedSupplierparts,
                        partName: currentLanguage === 'ar' ? 'دفعة مالية' : currentLanguage === 'fr' ? 'Paiement' : 'Payment',
                        price: -paymentAmount, // Negative amount for payment
                        paid: true,
                        partialPayment: 0,
                        paidAmount: paymentAmount,
                        status: 'payment',
                        date: new Date().toISOString(),
                        paidDate: new Date().toISOString(),
                        lastPaymentDate: new Date().toISOString(),
                        remarks: currentLanguage === 'ar' ? `دفعة مالية بمبلغ ${formatPrice(paymentAmount)}` :
                                currentLanguage === 'fr' ? `Paiement de ${formatPrice(paymentAmount)}` :
                                `Payment of ${formatPrice(paymentAmount)}`
                      };

                      // Add payment record to the data
                      updatedData.push(paymentRecord);

                      // Apply payment to transactions
                      for (const transaction of unpaidTransactions) {
                        if (remainingPayment <= 0) break;

                        const transactionAmount = parseFloat(transaction.price) || 0;
                        const currentPartialPayment = parseFloat(transaction.partialPayment) || 0;
                        const remainingTransactionAmount = transactionAmount - currentPartialPayment;

                        if (remainingPayment >= remainingTransactionAmount) {
                          // Pay full remaining amount
                          const index = updatedData.findIndex(t => t.id === transaction.id);
                          if (index !== -1) {
                            updatedData[index] = {
                              ...updatedData[index],
                              paid: true,
                              paidDate: new Date().toISOString(),
                              paidAmount: transactionAmount,
                              partialPayment: transactionAmount
                            };
                          }
                          remainingPayment -= remainingTransactionAmount;
                        } else {
                          // Partial payment
                          const index = updatedData.findIndex(t => t.id === transaction.id);
                          if (index !== -1) {
                            const newPartialPayment = currentPartialPayment + remainingPayment;
                            updatedData[index] = {
                              ...updatedData[index],
                              partialPayment: newPartialPayment,
                              lastPaymentDate: new Date().toISOString(),
                              paid: newPartialPayment >= transactionAmount,
                              paidAmount: newPartialPayment >= transactionAmount ? transactionAmount : 0,
                              paidDate: newPartialPayment >= transactionAmount ? new Date().toISOString() : null
                            };
                          }
                          remainingPayment = 0;
                        }
                      }

                      // Save updated data
                      saveSuppliersPartsReparationData(updatedData);

                      showToast(`💰 ${currentLanguage === 'ar' ? 'تم دفع المبلغ بنجاح' : currentLanguage === 'fr' ? 'Paiement effectué avec succès' : 'Payment processed successfully'} ${formatPrice(paymentAmount)}`, 'success', 2000);
                      setPaymentAmountparts('');
                      setShowSupplierpartsPayment(false);
                    }
                  }}
                >
                  <span className="btn-icon">💰</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'إضافة رصيد' : currentLanguage === 'fr' ? 'Ajouter le Solde' : 'Add Balance'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Supplier Transaction View Modal */}
      {showSupplierTransactionparts && selectedSupplierparts && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowSupplierTransactionparts(false)}>
          <div className={`modal-content supplier-transactions-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header supplier-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">👁️</div>
                <div className="header-text">
                  <h2>{currentLanguage === 'ar' ? 'معاملات المورد' : currentLanguage === 'fr' ? 'Transactions du Fournisseur' : 'Supplier Transactions'}</h2>
                  <p>{selectedSupplierparts}</p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={() => setShowSupplierTransactionparts(false)}>×</button>
            </div>

            <div className="supplier-transactions-content">
              {/* Supplier Contact Information */}
              <div className="supplier-info-section" style={{
                background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
                padding: '1rem',
                borderRadius: '8px',
                marginBottom: '1rem',
                border: '1px solid #dee2e6'
              }}>
                <h3 style={{
                  margin: '0 0 0.5rem 0',
                  color: '#037171',
                  fontSize: '1.1rem',
                  fontWeight: 'bold'
                }}>
                  {currentLanguage === 'ar' ? 'معلومات المورد' : currentLanguage === 'fr' ? 'Informations Fournisseur' : 'Supplier Information'}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '0.5rem',
                  fontSize: '0.9rem'
                }}>
                  {(() => {
                    const supplierInfo = suppliers.find(s => s.name === selectedSupplierparts);
                    return (
                      <>
                        <div><strong>{currentLanguage === 'ar' ? 'الاسم:' : currentLanguage === 'fr' ? 'Nom:' : 'Name:'}</strong> {selectedSupplierparts}</div>
                        {supplierInfo?.phone && <div><strong>{currentLanguage === 'ar' ? 'الهاتف:' : currentLanguage === 'fr' ? 'Téléphone:' : 'Phone:'}</strong> {supplierInfo.phone}</div>}
                        {supplierInfo?.email && <div><strong>{currentLanguage === 'ar' ? 'البريد:' : currentLanguage === 'fr' ? 'Email:' : 'Email:'}</strong> {supplierInfo.email}</div>}
                        {supplierInfo?.address && <div><strong>{currentLanguage === 'ar' ? 'العنوان:' : currentLanguage === 'fr' ? 'Adresse:' : 'Address:'}</strong> {supplierInfo.address}</div>}
                        {supplierInfo?.city && <div><strong>{currentLanguage === 'ar' ? 'المدينة:' : currentLanguage === 'fr' ? 'Ville:' : 'City:'}</strong> {supplierInfo.city}</div>}
                      </>
                    );
                  })()}
                </div>
              </div>

              {/* Global Summary */}
              <div className="global-summary" style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                gap: '1rem',
                marginBottom: '1rem'
              }}>
                {(() => {
                  const transactions = getSupplierTransactions(selectedSupplierparts);
                  const totalTransactions = transactions.length;
                  const totalAmount = transactions
                    .filter(t => (t.status === 'credit' || !t.status) && parseFloat(t.price) > 0)
                    .reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0);
                  const totalPaid = transactions
                    .filter(t => t.status === 'payment' || parseFloat(t.price) < 0)
                    .reduce((sum, t) => sum + Math.abs(parseFloat(t.price) || 0), 0) +
                    transactions
                    .filter(t => (t.status === 'credit' || !t.status))
                    .reduce((sum, t) => sum + (parseFloat(t.partialPayment) || 0), 0);
                  const pending = totalAmount - totalPaid;

                  return (
                    <>
                      <div className="summary-card" style={{
                        background: 'linear-gradient(135deg, #007bff, #0056b3)',
                        color: 'white',
                        padding: '1rem',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{totalTransactions}</div>
                        <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
                          {currentLanguage === 'ar' ? 'إجمالي المعاملات' : currentLanguage === 'fr' ? 'Total Transactions' : 'Total Transactions'}
                        </div>
                      </div>
                      <div className="summary-card" style={{
                        background: 'linear-gradient(135deg, #6f42c1, #5a2d91)',
                        color: 'white',
                        padding: '1rem',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{formatPrice(totalAmount)}</div>
                        <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
                          {currentLanguage === 'ar' ? 'المبلغ الإجمالي' : currentLanguage === 'fr' ? 'Montant Total' : 'Total Amount'}
                        </div>
                      </div>
                      <div className="summary-card" style={{
                        background: 'linear-gradient(135deg, #28a745, #1e7e34)',
                        color: 'white',
                        padding: '1rem',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{formatPrice(totalPaid)}</div>
                        <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
                          {currentLanguage === 'ar' ? 'مدفوع' : currentLanguage === 'fr' ? 'Payé' : 'Paid'}
                        </div>
                      </div>
                      <div className="summary-card" style={{
                        background: pending > 0 ? 'linear-gradient(135deg, #dc3545, #c82333)' : 'linear-gradient(135deg, #28a745, #1e7e34)',
                        color: 'white',
                        padding: '1rem',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        <div style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{formatPrice(pending)}</div>
                        <div style={{ fontSize: '0.85rem', opacity: '0.9' }}>
                          {currentLanguage === 'ar' ? 'في الانتظار' : currentLanguage === 'fr' ? 'En Attente' : 'Pending'}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>

              <div className="transactions-search">
                <input
                  type="text"
                  value={transactionpartsSearchFilter}
                  onChange={(e) => setTransactionpartsSearchFilter(e.target.value)}
                  placeholder={currentLanguage === 'ar' ? 'البحث في المعاملات...' : currentLanguage === 'fr' ? 'Rechercher dans les transactions...' : 'Search transactions...'}
                  className="modern-search-input"
                />
              </div>

              <div className="transactions-table-container">
                <table className="modern-table">
                  <thead>
                    <tr>
                      <th>{currentLanguage === 'ar' ? 'رقم الإصلاح' : currentLanguage === 'fr' ? 'ID Réparation' : 'Repair ID'}</th>
                      <th>{currentLanguage === 'ar' ? 'القطع' : currentLanguage === 'fr' ? 'Pièces' : 'Parts'}</th>
                      <th>{currentLanguage === 'ar' ? 'السعر' : currentLanguage === 'fr' ? 'Prix' : 'Price'}</th>
                      <th>{currentLanguage === 'ar' ? 'التاريخ' : currentLanguage === 'fr' ? 'Date' : 'Date'}</th>
                      <th>{currentLanguage === 'ar' ? 'الحالة' : currentLanguage === 'fr' ? 'Statut' : 'Status'}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getSupplierTransactions(selectedSupplierparts)
                      .filter(transaction =>
                        !transactionpartsSearchFilter ||
                        transaction.repairId?.toLowerCase().includes(transactionpartsSearchFilter.toLowerCase()) ||
                        transaction.remarks?.toLowerCase().includes(transactionpartsSearchFilter.toLowerCase())
                      )
                      .map(transaction => (
                        <tr key={transaction.id}>
                          <td>{transaction.repairId}</td>
                          <td>{(() => {
                            // Handle payment records
                            if (transaction.partName === 'PAYMENT_RECORD' || transaction.status === 'payment') {
                              return currentLanguage === 'ar' ? 'دفعة مالية' : currentLanguage === 'fr' ? 'Paiement' : 'Payment';
                            }
                            // Handle repair parts - show device info if available
                            if (transaction.repairId && repairsData.length > 0) {
                              const repair = repairsData.find(r => r.id === transaction.repairId);
                              if (repair) {
                                const deviceName = repair.deviceName || '';
                                const problemType = formatProblemType(repair.problemType) || '';
                                const clientName = repair.clientName || '';
                                return `${deviceName}${problemType ? ` - ${problemType}` : ''}${clientName ? ` (${clientName})` : ''}`;
                              }
                            }
                            // Handle remarks with payment info
                            if (transaction.remarks && transaction.remarks.startsWith('PAYMENT_')) {
                              const amount = transaction.remarks.replace('PAYMENT_', '');
                              return currentLanguage === 'ar' ? `دفعة مالية بمبلغ ${amount}` :
                                     currentLanguage === 'fr' ? `Paiement de ${amount}` :
                                     `Payment of ${amount}`;
                            }
                            // Default fallback
                            return transaction.partName || transaction.remarks || (currentLanguage === 'ar' ? 'قطع إصلاح' : currentLanguage === 'fr' ? 'Pièces de réparation' : 'Repair parts');
                          })()}</td>
                          <td>{formatPrice(transaction.price || 0)}</td>
                          <td>{new Date(transaction.date).toLocaleDateString()}</td>
                          <td>
                            <span className={`status-badge ${transaction.paid ? 'paid' : 'pending'}`}>
                              {transaction.paid ?
                                (currentLanguage === 'ar' ? 'مدفوع' : currentLanguage === 'fr' ? 'Payé' : 'Paid') :
                                (currentLanguage === 'ar' ? 'معلق' : currentLanguage === 'fr' ? 'En Attente' : 'Pending')
                              }
                            </span>
                          </td>
                        </tr>
                      ))
                    }
                  </tbody>
                </table>
              </div>

              <div className="transactions-actions">
                <button
                  className="btn-modern btn-secondary"
                  onClick={() => {
                    printSupplierTransactions(selectedSupplierparts);
                  }}
                >
                  <span className="btn-icon">🖨️</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'طباعة المفلترة' : currentLanguage === 'fr' ? 'Imprimer Filtrées' : 'Print Filtered'}</span>
                </button>
                <button
                  className="btn-modern btn-primary"
                  onClick={() => {
                    printSupplierTransactions(selectedSupplierparts);
                  }}
                >
                  <span className="btn-icon">🖨️</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'طباعة الكل' : currentLanguage === 'fr' ? 'Imprimer Tout' : 'Print All'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Supplier Edit Modal */}
      {showSupplierpartsEdit && selectedSupplierparts && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowSupplierpartsEdit(false)}>
          <div className={`modal-content supplier-edit-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header supplier-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">✏️</div>
                <div className="header-text">
                  <h2>{currentLanguage === 'ar' ? 'تعديل المورد' : currentLanguage === 'fr' ? 'Modifier le Fournisseur' : 'Edit Supplier'}</h2>
                  <p>{selectedSupplierparts}</p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={() => setShowSupplierpartsEdit(false)}>×</button>
            </div>

            <div className="supplier-edit-content">
              <div className="edit-form">
                <div className="form-grid">
                  <div className="form-field">
                    <label>{currentLanguage === 'ar' ? 'اسم المورد' : currentLanguage === 'fr' ? 'Nom du Fournisseur' : 'Supplier Name'}</label>
                    <input
                      type="text"
                      defaultValue={selectedSupplierparts}
                      className="modern-input"
                      placeholder={currentLanguage === 'ar' ? 'أدخل اسم المورد' : currentLanguage === 'fr' ? 'Entrez le nom du fournisseur' : 'Enter supplier name'}
                    />
                  </div>
                  <div className="form-field">
                    <label>{currentLanguage === 'ar' ? 'رقم الهاتف' : currentLanguage === 'fr' ? 'Numéro de Téléphone' : 'Phone Number'}</label>
                    <input
                      type="tel"
                      className="modern-input"
                      placeholder={currentLanguage === 'ar' ? 'أدخل رقم الهاتف' : currentLanguage === 'fr' ? 'Entrez le numéro de téléphone' : 'Enter phone number'}
                    />
                  </div>
                  <div className="form-field">
                    <label>{currentLanguage === 'ar' ? 'البريد الإلكتروني' : currentLanguage === 'fr' ? 'Email' : 'Email'}</label>
                    <input
                      type="email"
                      className="modern-input"
                      placeholder={currentLanguage === 'ar' ? 'أدخل البريد الإلكتروني' : currentLanguage === 'fr' ? 'Entrez l\'email' : 'Enter email'}
                    />
                  </div>
                  <div className="form-field">
                    <label>{currentLanguage === 'ar' ? 'العنوان' : currentLanguage === 'fr' ? 'Adresse' : 'Address'}</label>
                    <textarea
                      className="modern-textarea"
                      placeholder={currentLanguage === 'ar' ? 'أدخل العنوان' : currentLanguage === 'fr' ? 'Entrez l\'adresse' : 'Enter address'}
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              <div className="edit-actions">
                <button
                  className="btn-modern btn-secondary"
                  onClick={() => setShowSupplierpartsEdit(false)}
                >
                  <span className="btn-icon">❌</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'إلغاء' : currentLanguage === 'fr' ? 'Annuler' : 'Cancel'}</span>
                </button>
                <button
                  className="btn-modern btn-success"
                  onClick={() => {
                    showToast(`✅ ${currentLanguage === 'ar' ? 'تم تحديث المورد' : 'Supplier updated'} ${selectedSupplierparts}`, 'success', 2000);
                    setShowSupplierpartsEdit(false);
                  }}
                >
                  <span className="btn-icon">💾</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'حفظ التغييرات' : currentLanguage === 'fr' ? 'Sauvegarder' : 'Save Changes'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Supplier Delete Modal */}
      {showSupplierpartsDelete && selectedSupplierparts && (
        <div className="modal-overlay modern-overlay" onClick={() => setShowSupplierpartsDelete(false)}>
          <div className={`modal-content supplier-delete-modal lang-${currentLanguage}`} onClick={(e) => e.stopPropagation()}>
            <div className={`modal-header supplier-header danger-header ${currentLanguage !== 'ar' ? 'modal-header-ltr' : ''}`}>
              <div className="header-content">
                <div className="header-icon">🗑️</div>
                <div className="header-text">
                  <h2>{currentLanguage === 'ar' ? 'حذف المورد' : currentLanguage === 'fr' ? 'Supprimer le Fournisseur' : 'Delete Supplier'}</h2>
                  <p>{selectedSupplierparts}</p>
                </div>
              </div>
              <button className="modal-close modern-close" onClick={() => setShowSupplierpartsDelete(false)}>×</button>
            </div>

            <div className="supplier-delete-content">
              <div className="delete-warning">
                <div className="warning-icon">⚠️</div>
                <div className="warning-text">
                  <h3>{currentLanguage === 'ar' ? 'تحذير: هذا الإجراء لا يمكن التراجع عنه' : currentLanguage === 'fr' ? 'Attention: Cette action est irréversible' : 'Warning: This action cannot be undone'}</h3>
                  <p>{currentLanguage === 'ar' ? 'سيتم حذف جميع المعاملات المرتبطة بهذا المورد' : currentLanguage === 'fr' ? 'Toutes les transactions liées à ce fournisseur seront supprimées' : 'All transactions related to this supplier will be deleted'}</p>
                </div>
              </div>

              <div className="delete-summary">
                <div className="summary-item">
                  <span className="summary-label">{currentLanguage === 'ar' ? 'عدد المعاملات' : currentLanguage === 'fr' ? 'Nombre de Transactions' : 'Transaction Count'}:</span>
                  <span className="summary-value">{getSupplierTransactions(selectedSupplierparts).length}</span>
                </div>
                <div className="summary-item">
                  <span className="summary-label">{currentLanguage === 'ar' ? 'إجمالي القيمة' : currentLanguage === 'fr' ? 'Valeur Totale' : 'Total Value'}:</span>
                  <span className="summary-value">{formatPrice(getSupplierTransactions(selectedSupplierparts).reduce((sum, t) => sum + (parseFloat(t.price) || 0), 0))}</span>
                </div>
              </div>

              <div className="delete-verification">
                <label>{currentLanguage === 'ar' ? 'أدخل رمز التأكيد (1234)' : currentLanguage === 'fr' ? 'Entrez le code de confirmation (1234)' : 'Enter confirmation code (1234)'}</label>
                <input
                  type="password"
                  value={deletePasscodeparts}
                  onChange={(e) => setDeletePasscodeparts(e.target.value)}
                  placeholder="1234"
                  className="modern-input"
                />
              </div>

              <div className="delete-actions">
                <button
                  className="btn-modern btn-secondary"
                  onClick={() => {
                    setDeletePasscodeparts('');
                    setShowSupplierpartsDelete(false);
                  }}
                >
                  <span className="btn-icon">❌</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'إلغاء' : currentLanguage === 'fr' ? 'Annuler' : 'Cancel'}</span>
                </button>
                <button
                  className="btn-modern btn-danger"
                  disabled={deletePasscodeparts !== '1234'}
                  onClick={() => {
                    if (deletePasscodeparts === '1234') {
                      showToast(`🗑️ ${currentLanguage === 'ar' ? 'تم حذف المورد' : 'Supplier deleted'} ${selectedSupplierparts}`, 'success', 2000);
                      setDeletePasscodeparts('');
                      setShowSupplierpartsDelete(false);
                    }
                  }}
                >
                  <span className="btn-icon">🗑️</span>
                  <span className="btn-text">{currentLanguage === 'ar' ? 'حذف نهائي' : currentLanguage === 'fr' ? 'Supprimer Définitivement' : 'Delete Permanently'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Add Supplier Parts Modal */}
      {showAddSupplierModal && (
        <div className="modal-overlay modern-overlay" onClick={closeAddSupplierModal}>
          <div
            className={`modal-content add-supplier-modal lang-${currentLanguage}`}
            onClick={(e) => e.stopPropagation()}
            style={{
              direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
              textAlign: currentLanguage === 'ar' ? 'right' : 'left'
            }}
          >
            {/* Modern Header */}
            <div className={`add-supplier-header ${currentLanguage !== 'ar' ? 'header-ltr' : 'header-rtl'}`}>
              <div className="header-content-supplier">
                <div className="header-icon-supplier">🔧</div>
                <div className="header-text-supplier">
                  <h2 style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {currentLanguage === 'ar' ? 'إضافة مورد جديد' :
                     currentLanguage === 'fr' ? 'Ajouter Nouveau Fournisseur' :
                     'Add New Supplier'}
                  </h2>
                  <p style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {currentLanguage === 'ar' ? 'أدخل معلومات المورد وتفاصيل الاتصال' :
                     currentLanguage === 'fr' ? 'Entrez les informations du fournisseur et les détails de contact' :
                     'Enter supplier information and contact details'}
                  </p>
                </div>
              </div>
              <button
                className="modal-close-btn-supplier"
                onClick={closeAddSupplierModal}
                style={{
                  position: 'absolute',
                  top: '15px',
                  right: currentLanguage === 'ar' ? 'auto' : '15px',
                  left: currentLanguage === 'ar' ? '15px' : 'auto'
                }}
              >
                ✕
              </button>
            </div>

            {/* Form Content */}
            <div className="add-supplier-content">
              <div className="supplier-form-grid">
                {/* Supplier Information */}
                <div className="form-section">
                  <h3 className="section-title" style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {currentLanguage === 'ar' ? 'معلومات المورد' :
                     currentLanguage === 'fr' ? 'Informations Fournisseur' :
                     'Supplier Information'}
                  </h3>

                  <div className="form-group">
                    <label style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                      direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                      textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                    }}>
                      {currentLanguage === 'ar' ? 'اسم المورد *' :
                       currentLanguage === 'fr' ? 'Nom du Fournisseur *' :
                       'Supplier Name *'}
                    </label>
                    <input
                      type="text"
                      className="form-input"
                      value={newSupplierForm.supplierName}
                      onChange={(e) => setNewSupplierForm({...newSupplierForm, supplierName: e.target.value})}
                      placeholder={currentLanguage === 'ar' ? 'أدخل اسم المورد' :
                                  currentLanguage === 'fr' ? 'Entrez le nom du fournisseur' :
                                  'Enter supplier name'}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                        textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                      }}
                    />
                  </div>
                </div>



                {/* Additional Supplier Information */}
                <div className="form-section">
                  <h3 className="section-title" style={{
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                  }}>
                    {currentLanguage === 'ar' ? 'معلومات إضافية للمورد' :
                     currentLanguage === 'fr' ? 'Informations Supplémentaires Fournisseur' :
                     'Additional Supplier Information'}
                  </h3>

                  <div className="form-row">
                    <div className="form-group">
                      <label style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                        textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                      }}>
                        {currentLanguage === 'ar' ? 'رقم الهاتف (اختياري)' :
                         currentLanguage === 'fr' ? 'Numéro de Téléphone (optionnel)' :
                         'Phone Number (optional)'}
                      </label>
                      <input
                        type="tel"
                        className="form-input"
                        value={newSupplierForm.phone || ''}
                        onChange={(e) => setNewSupplierForm({...newSupplierForm, phone: e.target.value})}
                        placeholder={currentLanguage === 'ar' ? 'أدخل رقم الهاتف' :
                                    currentLanguage === 'fr' ? 'Entrez le numéro de téléphone' :
                                    'Enter phone number'}
                        style={{
                          fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                          direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                          textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                        }}
                      />
                    </div>

                    <div className="form-group">
                      <label style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                        textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                      }}>
                        {currentLanguage === 'ar' ? 'البريد الإلكتروني (اختياري)' :
                         currentLanguage === 'fr' ? 'Email (optionnel)' :
                         'Email (optional)'}
                      </label>
                      <input
                        type="email"
                        className="form-input"
                        value={newSupplierForm.email || ''}
                        onChange={(e) => setNewSupplierForm({...newSupplierForm, email: e.target.value})}
                        placeholder={currentLanguage === 'ar' ? 'أدخل البريد الإلكتروني' :
                                    currentLanguage === 'fr' ? 'Entrez l\'email' :
                                    'Enter email'}
                        style={{
                          fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                          direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                          textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                        }}
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                      direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                      textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                    }}>
                      {currentLanguage === 'ar' ? 'العنوان (اختياري)' :
                       currentLanguage === 'fr' ? 'Adresse (optionnel)' :
                       'Address (optional)'}
                    </label>
                    <textarea
                      className="form-textarea"
                      value={newSupplierForm.address || ''}
                      onChange={(e) => setNewSupplierForm({...newSupplierForm, address: e.target.value})}
                      placeholder={currentLanguage === 'ar' ? 'أدخل العنوان' :
                                  currentLanguage === 'fr' ? 'Entrez l\'adresse' :
                                  'Enter address'}
                      rows="2"
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                        textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                      }}
                    />
                  </div>

                  <div className="form-group">
                    <label style={{
                      fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                      direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                      textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                    }}>
                      {currentLanguage === 'ar' ? 'المدينة (اختياري)' :
                       currentLanguage === 'fr' ? 'Ville (optionnel)' :
                       'City (optional)'}
                    </label>
                    <input
                      type="text"
                      className="form-input"
                      value={newSupplierForm.city || ''}
                      onChange={(e) => setNewSupplierForm({...newSupplierForm, city: e.target.value})}
                      placeholder={currentLanguage === 'ar' ? 'أدخل المدينة' :
                                  currentLanguage === 'fr' ? 'Entrez la ville' :
                                  'Enter city'}
                      style={{
                        fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif',
                        direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                        textAlign: currentLanguage === 'ar' ? 'right' : 'left'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="supplier-actions">
                <button
                  className="btn-modern-supplier btn-primary-supplier"
                  onClick={handleAddSupplierPart}
                  style={{
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                >
                  <span className="btn-icon-supplier">✅</span>
                  <span className="btn-text-supplier">
                    {currentLanguage === 'ar' ? 'إضافة المورد' :
                     currentLanguage === 'fr' ? 'Ajouter Fournisseur' :
                     'Add Supplier'}
                  </span>
                </button>
                <button
                  className="btn-modern-supplier btn-secondary-supplier"
                  onClick={closeAddSupplierModal}
                  style={{
                    direction: currentLanguage === 'ar' ? 'rtl' : 'ltr',
                    fontFamily: currentLanguage === 'ar' ? 'Cairo, Tahoma, Arial, sans-serif' : 'Arial, sans-serif'
                  }}
                >
                  <span className="btn-icon-supplier">❌</span>
                  <span className="btn-text-supplier">
                    {currentLanguage === 'ar' ? 'إلغاء' :
                     currentLanguage === 'fr' ? 'Annuler' :
                     'Cancel'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modern Repair Details Modal */}
      {showRepairDetailsModal && selectedRepair && (
        <div className="modal-overlay" onClick={() => setShowRepairDetailsModal(false)}>
          <div
            className="modern-repair-details-modal"
            onClick={(e) => e.stopPropagation()}
            style={{
              background: 'white',
              borderRadius: '20px',
              padding: '0',
              maxWidth: '1200px',
              width: '95%',
              maxHeight: '90vh',
              overflow: 'hidden',
              boxShadow: '0 25px 50px rgba(0,0,0,0.25)',
              animation: 'modalSlideIn 0.3s ease-out'
            }}
          >
            {/* Header */}
            <div style={{
              background: 'linear-gradient(135deg, #29B6F6, #1E88E5)',
              color: 'white',
              padding: '2rem',
              textAlign: 'center',
              position: 'relative'
            }}>
              <button
                onClick={() => setShowRepairDetailsModal(false)}
                style={{
                  position: 'absolute',
                  top: '1rem',
                  right: '1rem',
                  background: 'rgba(255,255,255,0.2)',
                  border: 'none',
                  color: 'white',
                  borderRadius: '50%',
                  width: '40px',
                  height: '40px',
                  cursor: 'pointer',
                  fontSize: '1.2rem'
                }}
              >
                ✕
              </button>
              <h2 style={{
                margin: '0',
                fontSize: '2rem',
                fontWeight: '700'
              }}>
                {currentLanguage === 'ar' ? 'تفاصيل الإصلاح' : currentLanguage === 'fr' ? 'Détails de Réparation' : 'Repair Details'}
              </h2>
              <p style={{
                margin: '0.5rem 0 0 0',
                fontSize: '1.1rem',
                opacity: '0.9'
              }}>
                #{selectedRepair.id}
              </p>
            </div>

            {/* Content */}
            <div style={{
              padding: '2rem',
              maxHeight: 'calc(90vh - 200px)',
              overflowY: 'auto'
            }}>
              {/* Main Info Grid */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '2rem',
                marginBottom: '2rem'
              }}>
                {/* Client Info */}
                <div style={{
                  background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
                  padding: '1.5rem',
                  borderRadius: '15px',
                  border: '1px solid #dee2e6'
                }}>
                  <h3 style={{
                    margin: '0 0 1rem 0',
                    color: '#29B6F6',
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    👤 {currentLanguage === 'ar' ? 'معلومات العميل' : currentLanguage === 'fr' ? 'Informations Client' : 'Client Information'}
                  </h3>
                  <div style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                    <p><strong>{currentLanguage === 'ar' ? 'الاسم:' : currentLanguage === 'fr' ? 'Nom:' : 'Name:'}</strong> {selectedRepair.clientName}</p>
                    <p><strong>{currentLanguage === 'ar' ? 'الهاتف:' : currentLanguage === 'fr' ? 'Téléphone:' : 'Phone:'}</strong> {selectedRepair.clientPhone}</p>
                    <p><strong>{currentLanguage === 'ar' ? 'تاريخ الإيداع:' : currentLanguage === 'fr' ? 'Date de Dépôt:' : 'Deposit Date:'}</strong> {new Date(selectedRepair.createdAt || selectedRepair.depositDate).toLocaleDateString()}</p>
                  </div>
                </div>

                {/* Device Info */}
                <div style={{
                  background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
                  padding: '1.5rem',
                  borderRadius: '15px',
                  border: '1px solid #dee2e6'
                }}>
                  <h3 style={{
                    margin: '0 0 1rem 0',
                    color: '#29B6F6',
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    📱 {currentLanguage === 'ar' ? 'معلومات الجهاز' : currentLanguage === 'fr' ? 'Informations Appareil' : 'Device Information'}
                  </h3>
                  <div style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                    <p><strong>{currentLanguage === 'ar' ? 'الجهاز:' : currentLanguage === 'fr' ? 'Appareil:' : 'Device:'}</strong> {selectedRepair.deviceName}</p>
                    <p><strong>{currentLanguage === 'ar' ? 'النوع:' : currentLanguage === 'fr' ? 'Type:' : 'Type:'}</strong> {selectedRepair.deviceType}</p>
                    <p><strong>{currentLanguage === 'ar' ? 'المشكلة:' : currentLanguage === 'fr' ? 'Problème:' : 'Problem:'}</strong> {formatProblemType(selectedRepair.problemType)}</p>
                  </div>
                </div>
              </div>

              {/* Status & Pricing */}
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '1.5rem',
                marginBottom: '2rem'
              }}>
                {/* Status */}
                <div style={{
                  background: selectedRepair.status === 'done' ? 'linear-gradient(135deg, #d4edda, #c3e6cb)' :
                             selectedRepair.status === 'echec' ? 'linear-gradient(135deg, #f8d7da, #f5c6cb)' :
                             selectedRepair.status === 'inProgress' ? 'linear-gradient(135deg, #cce7ff, #b3d9ff)' :
                             'linear-gradient(135deg, #fff3cd, #ffeaa7)',
                  padding: '1.5rem',
                  borderRadius: '15px',
                  textAlign: 'center',
                  border: '2px solid ' + (selectedRepair.status === 'done' ? '#28a745' :
                                         selectedRepair.status === 'echec' ? '#dc3545' :
                                         selectedRepair.status === 'inProgress' ? '#29B6F6' : '#ffc107')
                }}>
                  <h3 style={{
                    margin: '0 0 0.5rem 0',
                    color: selectedRepair.status === 'done' ? '#155724' :
                           selectedRepair.status === 'echec' ? '#721c24' :
                           selectedRepair.status === 'inProgress' ? '#0c5460' : '#856404',
                    fontSize: '1.2rem'
                  }}>
                    {currentLanguage === 'ar' ? 'الحالة' : currentLanguage === 'fr' ? 'Statut' : 'Status'}
                  </h3>
                  <div style={{
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                    color: selectedRepair.status === 'done' ? '#155724' :
                           selectedRepair.status === 'echec' ? '#721c24' :
                           selectedRepair.status === 'inProgress' ? '#0c5460' : '#856404'
                  }}>
                    {translateRepairStatus(selectedRepair.status)}
                  </div>
                </div>

                {/* Pricing */}
                <div style={{
                  background: 'linear-gradient(135deg, #e8f5e8, #d4edda)',
                  padding: '1.5rem',
                  borderRadius: '15px',
                  textAlign: 'center',
                  border: '2px solid #28a745'
                }}>
                  <h3 style={{
                    margin: '0 0 0.5rem 0',
                    color: '#155724',
                    fontSize: '1.2rem'
                  }}>
                    {currentLanguage === 'ar' ? 'السعر النهائي' : currentLanguage === 'fr' ? 'Prix Final' : 'Final Price'}
                  </h3>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    color: '#155724'
                  }}>
                    {selectedRepair.isFailedRepair || selectedRepair.status === 'enAttenteClient'
                      ? formatPrice(selectedRepair.verificationPrice || 0)
                      : formatPrice(selectedRepair.repairPrice || 0)
                    }
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              {(selectedRepair.problemDescription || selectedRepair.remarks) && (
                <div style={{
                  background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
                  padding: '1.5rem',
                  borderRadius: '15px',
                  border: '1px solid #dee2e6'
                }}>
                  <h3 style={{
                    margin: '0 0 1rem 0',
                    color: '#29B6F6',
                    fontSize: '1.3rem',
                    fontWeight: '600'
                  }}>
                    📝 {currentLanguage === 'ar' ? 'معلومات إضافية' : currentLanguage === 'fr' ? 'Informations Supplémentaires' : 'Additional Information'}
                  </h3>
                  {selectedRepair.problemDescription && (
                    <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}>
                      <strong>{currentLanguage === 'ar' ? 'وصف المشكلة:' : currentLanguage === 'fr' ? 'Description du Problème:' : 'Problem Description:'}</strong> {selectedRepair.problemDescription}
                    </p>
                  )}
                  {selectedRepair.remarks && (
                    <p style={{ fontSize: '1.1rem', margin: '0' }}>
                      <strong>{currentLanguage === 'ar' ? 'ملاحظات:' : currentLanguage === 'fr' ? 'Remarques:' : 'Remarks:'}</strong> {selectedRepair.remarks}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      </div>
    </div>
  );
}
